import { FileListProps } from '../../components/Attachments/types.d.ts';
import { FilesCardProps, FilesType } from '../../components/FilesCard/types.d.ts';
export type SelfFilesCardProps = FilesCardProps & {
    id?: number;
};
export declare function genDefaultFiles(count?: number): SelfFilesCardProps[];
export declare function useAttachmentStubs(initValue?: SelfFilesCardProps[]): {
    files: globalThis.Ref<{
        uid?: string | number | undefined;
        name?: string | undefined;
        fileSize?: number | undefined;
        fileType?: FilesType | undefined;
        description?: string | undefined;
        url?: string | undefined;
        thumbUrl?: string | undefined;
        imgFile?: {
            readonly lastModified: number;
            readonly name: string;
            readonly webkitRelativePath: string;
            readonly size: number;
            readonly type: string;
            arrayBuffer: () => Promise<ArrayBuffer>;
            bytes: () => Promise<Uint8Array>;
            slice: (start?: number, end?: number, contentType?: string) => Blob;
            stream: () => ReadableStream<Uint8Array>;
            text: () => Promise<string>;
        } | {
            readonly size: number;
            readonly type: string;
            arrayBuffer: () => Promise<ArrayBuffer>;
            bytes: () => Promise<Uint8Array>;
            slice: (start?: number, end?: number, contentType?: string) => Blob;
            stream: () => ReadableStream<Uint8Array>;
            text: () => Promise<string>;
        } | undefined;
        iconSize?: string | undefined;
        iconColor?: string | undefined;
        showDelIcon?: boolean | undefined;
        maxWidth?: string | undefined;
        noStyle?: boolean | undefined;
        style?: {
            [x: `--${string}`]: string | number | undefined;
            accentColor?: import("csstype").Property.AccentColor | undefined;
            alignContent?: import("csstype").Property.AlignContent | undefined;
            alignItems?: import("csstype").Property.AlignItems | undefined;
            alignSelf?: import("csstype").Property.AlignSelf | undefined;
            alignTracks?: import("csstype").Property.AlignTracks | undefined;
            animationComposition?: import("csstype").Property.AnimationComposition | undefined;
            animationDelay?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            animationDirection?: import("csstype").Property.AnimationDirection | undefined;
            animationDuration?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            animationFillMode?: import("csstype").Property.AnimationFillMode | undefined;
            animationIterationCount?: import("csstype").Property.AnimationIterationCount | undefined;
            animationName?: import("csstype").Property.AnimationName | undefined;
            animationPlayState?: import("csstype").Property.AnimationPlayState | undefined;
            animationRangeEnd?: import("csstype").Property.AnimationRangeEnd<string | number> | undefined;
            animationRangeStart?: import("csstype").Property.AnimationRangeStart<string | number> | undefined;
            animationTimeline?: import("csstype").Property.AnimationTimeline | undefined;
            animationTimingFunction?: import("csstype").Property.AnimationTimingFunction | undefined;
            appearance?: import("csstype").Property.Appearance | undefined;
            aspectRatio?: import("csstype").Property.AspectRatio | undefined;
            backdropFilter?: import("csstype").Property.BackdropFilter | undefined;
            backfaceVisibility?: import("csstype").Property.BackfaceVisibility | undefined;
            backgroundAttachment?: import("csstype").Property.BackgroundAttachment | undefined;
            backgroundBlendMode?: import("csstype").Property.BackgroundBlendMode | undefined;
            backgroundClip?: import("csstype").Property.BackgroundClip | undefined;
            backgroundColor?: import("csstype").Property.BackgroundColor | undefined;
            backgroundImage?: import("csstype").Property.BackgroundImage | undefined;
            backgroundOrigin?: import("csstype").Property.BackgroundOrigin | undefined;
            backgroundPositionX?: import("csstype").Property.BackgroundPositionX<string | number> | undefined;
            backgroundPositionY?: import("csstype").Property.BackgroundPositionY<string | number> | undefined;
            backgroundRepeat?: import("csstype").Property.BackgroundRepeat | undefined;
            backgroundSize?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            blockOverflow?: import("csstype").Property.BlockOverflow | undefined;
            blockSize?: import("csstype").Property.BlockSize<string | number> | undefined;
            borderBlockColor?: import("csstype").Property.BorderBlockColor | undefined;
            borderBlockEndColor?: import("csstype").Property.BorderBlockEndColor | undefined;
            borderBlockEndStyle?: import("csstype").Property.BorderBlockEndStyle | undefined;
            borderBlockEndWidth?: import("csstype").Property.BorderBlockEndWidth<string | number> | undefined;
            borderBlockStartColor?: import("csstype").Property.BorderBlockStartColor | undefined;
            borderBlockStartStyle?: import("csstype").Property.BorderBlockStartStyle | undefined;
            borderBlockStartWidth?: import("csstype").Property.BorderBlockStartWidth<string | number> | undefined;
            borderBlockStyle?: import("csstype").Property.BorderBlockStyle | undefined;
            borderBlockWidth?: import("csstype").Property.BorderBlockWidth<string | number> | undefined;
            borderBottomColor?: import("csstype").Property.BorderBottomColor | undefined;
            borderBottomLeftRadius?: import("csstype").Property.BorderBottomLeftRadius<string | number> | undefined;
            borderBottomRightRadius?: import("csstype").Property.BorderBottomRightRadius<string | number> | undefined;
            borderBottomStyle?: import("csstype").Property.BorderBottomStyle | undefined;
            borderBottomWidth?: import("csstype").Property.BorderBottomWidth<string | number> | undefined;
            borderCollapse?: import("csstype").Property.BorderCollapse | undefined;
            borderEndEndRadius?: import("csstype").Property.BorderEndEndRadius<string | number> | undefined;
            borderEndStartRadius?: import("csstype").Property.BorderEndStartRadius<string | number> | undefined;
            borderImageOutset?: import("csstype").Property.BorderImageOutset<string | number> | undefined;
            borderImageRepeat?: import("csstype").Property.BorderImageRepeat | undefined;
            borderImageSlice?: import("csstype").Property.BorderImageSlice | undefined;
            borderImageSource?: import("csstype").Property.BorderImageSource | undefined;
            borderImageWidth?: import("csstype").Property.BorderImageWidth<string | number> | undefined;
            borderInlineColor?: import("csstype").Property.BorderInlineColor | undefined;
            borderInlineEndColor?: import("csstype").Property.BorderInlineEndColor | undefined;
            borderInlineEndStyle?: import("csstype").Property.BorderInlineEndStyle | undefined;
            borderInlineEndWidth?: import("csstype").Property.BorderInlineEndWidth<string | number> | undefined;
            borderInlineStartColor?: import("csstype").Property.BorderInlineStartColor | undefined;
            borderInlineStartStyle?: import("csstype").Property.BorderInlineStartStyle | undefined;
            borderInlineStartWidth?: import("csstype").Property.BorderInlineStartWidth<string | number> | undefined;
            borderInlineStyle?: import("csstype").Property.BorderInlineStyle | undefined;
            borderInlineWidth?: import("csstype").Property.BorderInlineWidth<string | number> | undefined;
            borderLeftColor?: import("csstype").Property.BorderLeftColor | undefined;
            borderLeftStyle?: import("csstype").Property.BorderLeftStyle | undefined;
            borderLeftWidth?: import("csstype").Property.BorderLeftWidth<string | number> | undefined;
            borderRightColor?: import("csstype").Property.BorderRightColor | undefined;
            borderRightStyle?: import("csstype").Property.BorderRightStyle | undefined;
            borderRightWidth?: import("csstype").Property.BorderRightWidth<string | number> | undefined;
            borderSpacing?: import("csstype").Property.BorderSpacing<string | number> | undefined;
            borderStartEndRadius?: import("csstype").Property.BorderStartEndRadius<string | number> | undefined;
            borderStartStartRadius?: import("csstype").Property.BorderStartStartRadius<string | number> | undefined;
            borderTopColor?: import("csstype").Property.BorderTopColor | undefined;
            borderTopLeftRadius?: import("csstype").Property.BorderTopLeftRadius<string | number> | undefined;
            borderTopRightRadius?: import("csstype").Property.BorderTopRightRadius<string | number> | undefined;
            borderTopStyle?: import("csstype").Property.BorderTopStyle | undefined;
            borderTopWidth?: import("csstype").Property.BorderTopWidth<string | number> | undefined;
            bottom?: import("csstype").Property.Bottom<string | number> | undefined;
            boxDecorationBreak?: import("csstype").Property.BoxDecorationBreak | undefined;
            boxShadow?: import("csstype").Property.BoxShadow | undefined;
            boxSizing?: import("csstype").Property.BoxSizing | undefined;
            breakAfter?: import("csstype").Property.BreakAfter | undefined;
            breakBefore?: import("csstype").Property.BreakBefore | undefined;
            breakInside?: import("csstype").Property.BreakInside | undefined;
            captionSide?: import("csstype").Property.CaptionSide | undefined;
            caretColor?: import("csstype").Property.CaretColor | undefined;
            caretShape?: import("csstype").Property.CaretShape | undefined;
            clear?: import("csstype").Property.Clear | undefined;
            clipPath?: import("csstype").Property.ClipPath | undefined;
            color?: import("csstype").Property.Color | undefined;
            colorAdjust?: import("csstype").Property.PrintColorAdjust | undefined;
            colorScheme?: import("csstype").Property.ColorScheme | undefined;
            columnCount?: import("csstype").Property.ColumnCount | undefined;
            columnFill?: import("csstype").Property.ColumnFill | undefined;
            columnGap?: import("csstype").Property.ColumnGap<string | number> | undefined;
            columnRuleColor?: import("csstype").Property.ColumnRuleColor | undefined;
            columnRuleStyle?: import("csstype").Property.ColumnRuleStyle | undefined;
            columnRuleWidth?: import("csstype").Property.ColumnRuleWidth<string | number> | undefined;
            columnSpan?: import("csstype").Property.ColumnSpan | undefined;
            columnWidth?: import("csstype").Property.ColumnWidth<string | number> | undefined;
            contain?: import("csstype").Property.Contain | undefined;
            containIntrinsicBlockSize?: import("csstype").Property.ContainIntrinsicBlockSize<string | number> | undefined;
            containIntrinsicHeight?: import("csstype").Property.ContainIntrinsicHeight<string | number> | undefined;
            containIntrinsicInlineSize?: import("csstype").Property.ContainIntrinsicInlineSize<string | number> | undefined;
            containIntrinsicWidth?: import("csstype").Property.ContainIntrinsicWidth<string | number> | undefined;
            containerName?: import("csstype").Property.ContainerName | undefined;
            containerType?: import("csstype").Property.ContainerType | undefined;
            content?: import("csstype").Property.Content | undefined;
            contentVisibility?: import("csstype").Property.ContentVisibility | undefined;
            counterIncrement?: import("csstype").Property.CounterIncrement | undefined;
            counterReset?: import("csstype").Property.CounterReset | undefined;
            counterSet?: import("csstype").Property.CounterSet | undefined;
            cursor?: import("csstype").Property.Cursor | undefined;
            direction?: import("csstype").Property.Direction | undefined;
            display?: import("csstype").Property.Display | undefined;
            emptyCells?: import("csstype").Property.EmptyCells | undefined;
            filter?: import("csstype").Property.Filter | undefined;
            flexBasis?: import("csstype").Property.FlexBasis<string | number> | undefined;
            flexDirection?: import("csstype").Property.FlexDirection | undefined;
            flexGrow?: import("csstype").Property.FlexGrow | undefined;
            flexShrink?: import("csstype").Property.FlexShrink | undefined;
            flexWrap?: import("csstype").Property.FlexWrap | undefined;
            float?: import("csstype").Property.Float | undefined;
            fontFamily?: import("csstype").Property.FontFamily | undefined;
            fontFeatureSettings?: import("csstype").Property.FontFeatureSettings | undefined;
            fontKerning?: import("csstype").Property.FontKerning | undefined;
            fontLanguageOverride?: import("csstype").Property.FontLanguageOverride | undefined;
            fontOpticalSizing?: import("csstype").Property.FontOpticalSizing | undefined;
            fontPalette?: import("csstype").Property.FontPalette | undefined;
            fontSize?: import("csstype").Property.FontSize<string | number> | undefined;
            fontSizeAdjust?: import("csstype").Property.FontSizeAdjust | undefined;
            fontSmooth?: import("csstype").Property.FontSmooth<string | number> | undefined;
            fontStretch?: import("csstype").Property.FontStretch | undefined;
            fontStyle?: import("csstype").Property.FontStyle | undefined;
            fontSynthesis?: import("csstype").Property.FontSynthesis | undefined;
            fontSynthesisPosition?: import("csstype").Property.FontSynthesisPosition | undefined;
            fontSynthesisSmallCaps?: import("csstype").Property.FontSynthesisSmallCaps | undefined;
            fontSynthesisStyle?: import("csstype").Property.FontSynthesisStyle | undefined;
            fontSynthesisWeight?: import("csstype").Property.FontSynthesisWeight | undefined;
            fontVariant?: import("csstype").Property.FontVariant | undefined;
            fontVariantAlternates?: import("csstype").Property.FontVariantAlternates | undefined;
            fontVariantCaps?: import("csstype").Property.FontVariantCaps | undefined;
            fontVariantEastAsian?: import("csstype").Property.FontVariantEastAsian | undefined;
            fontVariantEmoji?: import("csstype").Property.FontVariantEmoji | undefined;
            fontVariantLigatures?: import("csstype").Property.FontVariantLigatures | undefined;
            fontVariantNumeric?: import("csstype").Property.FontVariantNumeric | undefined;
            fontVariantPosition?: import("csstype").Property.FontVariantPosition | undefined;
            fontVariationSettings?: import("csstype").Property.FontVariationSettings | undefined;
            fontWeight?: import("csstype").Property.FontWeight | undefined;
            forcedColorAdjust?: import("csstype").Property.ForcedColorAdjust | undefined;
            gridAutoColumns?: import("csstype").Property.GridAutoColumns<string | number> | undefined;
            gridAutoFlow?: import("csstype").Property.GridAutoFlow | undefined;
            gridAutoRows?: import("csstype").Property.GridAutoRows<string | number> | undefined;
            gridColumnEnd?: import("csstype").Property.GridColumnEnd | undefined;
            gridColumnStart?: import("csstype").Property.GridColumnStart | undefined;
            gridRowEnd?: import("csstype").Property.GridRowEnd | undefined;
            gridRowStart?: import("csstype").Property.GridRowStart | undefined;
            gridTemplateAreas?: import("csstype").Property.GridTemplateAreas | undefined;
            gridTemplateColumns?: import("csstype").Property.GridTemplateColumns<string | number> | undefined;
            gridTemplateRows?: import("csstype").Property.GridTemplateRows<string | number> | undefined;
            hangingPunctuation?: import("csstype").Property.HangingPunctuation | undefined;
            height?: import("csstype").Property.Height<string | number> | undefined;
            hyphenateCharacter?: import("csstype").Property.HyphenateCharacter | undefined;
            hyphenateLimitChars?: import("csstype").Property.HyphenateLimitChars | undefined;
            hyphens?: import("csstype").Property.Hyphens | undefined;
            imageOrientation?: import("csstype").Property.ImageOrientation | undefined;
            imageRendering?: import("csstype").Property.ImageRendering | undefined;
            imageResolution?: import("csstype").Property.ImageResolution | undefined;
            initialLetter?: import("csstype").Property.InitialLetter | undefined;
            inlineSize?: import("csstype").Property.InlineSize<string | number> | undefined;
            inputSecurity?: import("csstype").Property.InputSecurity | undefined;
            insetBlockEnd?: import("csstype").Property.InsetBlockEnd<string | number> | undefined;
            insetBlockStart?: import("csstype").Property.InsetBlockStart<string | number> | undefined;
            insetInlineEnd?: import("csstype").Property.InsetInlineEnd<string | number> | undefined;
            insetInlineStart?: import("csstype").Property.InsetInlineStart<string | number> | undefined;
            isolation?: import("csstype").Property.Isolation | undefined;
            justifyContent?: import("csstype").Property.JustifyContent | undefined;
            justifyItems?: import("csstype").Property.JustifyItems | undefined;
            justifySelf?: import("csstype").Property.JustifySelf | undefined;
            justifyTracks?: import("csstype").Property.JustifyTracks | undefined;
            left?: import("csstype").Property.Left<string | number> | undefined;
            letterSpacing?: import("csstype").Property.LetterSpacing<string | number> | undefined;
            lineBreak?: import("csstype").Property.LineBreak | undefined;
            lineHeight?: import("csstype").Property.LineHeight<string | number> | undefined;
            lineHeightStep?: import("csstype").Property.LineHeightStep<string | number> | undefined;
            listStyleImage?: import("csstype").Property.ListStyleImage | undefined;
            listStylePosition?: import("csstype").Property.ListStylePosition | undefined;
            listStyleType?: import("csstype").Property.ListStyleType | undefined;
            marginBlockEnd?: import("csstype").Property.MarginBlockEnd<string | number> | undefined;
            marginBlockStart?: import("csstype").Property.MarginBlockStart<string | number> | undefined;
            marginBottom?: import("csstype").Property.MarginBottom<string | number> | undefined;
            marginInlineEnd?: import("csstype").Property.MarginInlineEnd<string | number> | undefined;
            marginInlineStart?: import("csstype").Property.MarginInlineStart<string | number> | undefined;
            marginLeft?: import("csstype").Property.MarginLeft<string | number> | undefined;
            marginRight?: import("csstype").Property.MarginRight<string | number> | undefined;
            marginTop?: import("csstype").Property.MarginTop<string | number> | undefined;
            marginTrim?: import("csstype").Property.MarginTrim | undefined;
            maskBorderMode?: import("csstype").Property.MaskBorderMode | undefined;
            maskBorderOutset?: import("csstype").Property.MaskBorderOutset<string | number> | undefined;
            maskBorderRepeat?: import("csstype").Property.MaskBorderRepeat | undefined;
            maskBorderSlice?: import("csstype").Property.MaskBorderSlice | undefined;
            maskBorderSource?: import("csstype").Property.MaskBorderSource | undefined;
            maskBorderWidth?: import("csstype").Property.MaskBorderWidth<string | number> | undefined;
            maskClip?: import("csstype").Property.MaskClip | undefined;
            maskComposite?: import("csstype").Property.MaskComposite | undefined;
            maskImage?: import("csstype").Property.MaskImage | undefined;
            maskMode?: import("csstype").Property.MaskMode | undefined;
            maskOrigin?: import("csstype").Property.MaskOrigin | undefined;
            maskPosition?: import("csstype").Property.MaskPosition<string | number> | undefined;
            maskRepeat?: import("csstype").Property.MaskRepeat | undefined;
            maskSize?: import("csstype").Property.MaskSize<string | number> | undefined;
            maskType?: import("csstype").Property.MaskType | undefined;
            masonryAutoFlow?: import("csstype").Property.MasonryAutoFlow | undefined;
            mathDepth?: import("csstype").Property.MathDepth | undefined;
            mathShift?: import("csstype").Property.MathShift | undefined;
            mathStyle?: import("csstype").Property.MathStyle | undefined;
            maxBlockSize?: import("csstype").Property.MaxBlockSize<string | number> | undefined;
            maxHeight?: import("csstype").Property.MaxHeight<string | number> | undefined;
            maxInlineSize?: import("csstype").Property.MaxInlineSize<string | number> | undefined;
            maxLines?: import("csstype").Property.MaxLines | undefined;
            maxWidth?: import("csstype").Property.MaxWidth<string | number> | undefined;
            minBlockSize?: import("csstype").Property.MinBlockSize<string | number> | undefined;
            minHeight?: import("csstype").Property.MinHeight<string | number> | undefined;
            minInlineSize?: import("csstype").Property.MinInlineSize<string | number> | undefined;
            minWidth?: import("csstype").Property.MinWidth<string | number> | undefined;
            mixBlendMode?: import("csstype").Property.MixBlendMode | undefined;
            motionDistance?: import("csstype").Property.OffsetDistance<string | number> | undefined;
            motionPath?: import("csstype").Property.OffsetPath | undefined;
            motionRotation?: import("csstype").Property.OffsetRotate | undefined;
            objectFit?: import("csstype").Property.ObjectFit | undefined;
            objectPosition?: import("csstype").Property.ObjectPosition<string | number> | undefined;
            offsetAnchor?: import("csstype").Property.OffsetAnchor<string | number> | undefined;
            offsetDistance?: import("csstype").Property.OffsetDistance<string | number> | undefined;
            offsetPath?: import("csstype").Property.OffsetPath | undefined;
            offsetPosition?: import("csstype").Property.OffsetPosition<string | number> | undefined;
            offsetRotate?: import("csstype").Property.OffsetRotate | undefined;
            offsetRotation?: import("csstype").Property.OffsetRotate | undefined;
            opacity?: import("csstype").Property.Opacity | undefined;
            order?: import("csstype").Property.Order | undefined;
            orphans?: import("csstype").Property.Orphans | undefined;
            outlineColor?: import("csstype").Property.OutlineColor | undefined;
            outlineOffset?: import("csstype").Property.OutlineOffset<string | number> | undefined;
            outlineStyle?: import("csstype").Property.OutlineStyle | undefined;
            outlineWidth?: import("csstype").Property.OutlineWidth<string | number> | undefined;
            overflowAnchor?: import("csstype").Property.OverflowAnchor | undefined;
            overflowBlock?: import("csstype").Property.OverflowBlock | undefined;
            overflowClipBox?: import("csstype").Property.OverflowClipBox | undefined;
            overflowClipMargin?: import("csstype").Property.OverflowClipMargin<string | number> | undefined;
            overflowInline?: import("csstype").Property.OverflowInline | undefined;
            overflowWrap?: import("csstype").Property.OverflowWrap | undefined;
            overflowX?: import("csstype").Property.OverflowX | undefined;
            overflowY?: import("csstype").Property.OverflowY | undefined;
            overlay?: import("csstype").Property.Overlay | undefined;
            overscrollBehaviorBlock?: import("csstype").Property.OverscrollBehaviorBlock | undefined;
            overscrollBehaviorInline?: import("csstype").Property.OverscrollBehaviorInline | undefined;
            overscrollBehaviorX?: import("csstype").Property.OverscrollBehaviorX | undefined;
            overscrollBehaviorY?: import("csstype").Property.OverscrollBehaviorY | undefined;
            paddingBlockEnd?: import("csstype").Property.PaddingBlockEnd<string | number> | undefined;
            paddingBlockStart?: import("csstype").Property.PaddingBlockStart<string | number> | undefined;
            paddingBottom?: import("csstype").Property.PaddingBottom<string | number> | undefined;
            paddingInlineEnd?: import("csstype").Property.PaddingInlineEnd<string | number> | undefined;
            paddingInlineStart?: import("csstype").Property.PaddingInlineStart<string | number> | undefined;
            paddingLeft?: import("csstype").Property.PaddingLeft<string | number> | undefined;
            paddingRight?: import("csstype").Property.PaddingRight<string | number> | undefined;
            paddingTop?: import("csstype").Property.PaddingTop<string | number> | undefined;
            page?: import("csstype").Property.Page | undefined;
            pageBreakAfter?: import("csstype").Property.PageBreakAfter | undefined;
            pageBreakBefore?: import("csstype").Property.PageBreakBefore | undefined;
            pageBreakInside?: import("csstype").Property.PageBreakInside | undefined;
            paintOrder?: import("csstype").Property.PaintOrder | undefined;
            perspective?: import("csstype").Property.Perspective<string | number> | undefined;
            perspectiveOrigin?: import("csstype").Property.PerspectiveOrigin<string | number> | undefined;
            pointerEvents?: import("csstype").Property.PointerEvents | undefined;
            position?: import("csstype").Property.Position | undefined;
            printColorAdjust?: import("csstype").Property.PrintColorAdjust | undefined;
            quotes?: import("csstype").Property.Quotes | undefined;
            resize?: import("csstype").Property.Resize | undefined;
            right?: import("csstype").Property.Right<string | number> | undefined;
            rotate?: import("csstype").Property.Rotate | undefined;
            rowGap?: import("csstype").Property.RowGap<string | number> | undefined;
            rubyAlign?: import("csstype").Property.RubyAlign | undefined;
            rubyMerge?: import("csstype").Property.RubyMerge | undefined;
            rubyPosition?: import("csstype").Property.RubyPosition | undefined;
            scale?: import("csstype").Property.Scale | undefined;
            scrollBehavior?: import("csstype").Property.ScrollBehavior | undefined;
            scrollMarginBlockEnd?: import("csstype").Property.ScrollMarginBlockEnd<string | number> | undefined;
            scrollMarginBlockStart?: import("csstype").Property.ScrollMarginBlockStart<string | number> | undefined;
            scrollMarginBottom?: import("csstype").Property.ScrollMarginBottom<string | number> | undefined;
            scrollMarginInlineEnd?: import("csstype").Property.ScrollMarginInlineEnd<string | number> | undefined;
            scrollMarginInlineStart?: import("csstype").Property.ScrollMarginInlineStart<string | number> | undefined;
            scrollMarginLeft?: import("csstype").Property.ScrollMarginLeft<string | number> | undefined;
            scrollMarginRight?: import("csstype").Property.ScrollMarginRight<string | number> | undefined;
            scrollMarginTop?: import("csstype").Property.ScrollMarginTop<string | number> | undefined;
            scrollPaddingBlockEnd?: import("csstype").Property.ScrollPaddingBlockEnd<string | number> | undefined;
            scrollPaddingBlockStart?: import("csstype").Property.ScrollPaddingBlockStart<string | number> | undefined;
            scrollPaddingBottom?: import("csstype").Property.ScrollPaddingBottom<string | number> | undefined;
            scrollPaddingInlineEnd?: import("csstype").Property.ScrollPaddingInlineEnd<string | number> | undefined;
            scrollPaddingInlineStart?: import("csstype").Property.ScrollPaddingInlineStart<string | number> | undefined;
            scrollPaddingLeft?: import("csstype").Property.ScrollPaddingLeft<string | number> | undefined;
            scrollPaddingRight?: import("csstype").Property.ScrollPaddingRight<string | number> | undefined;
            scrollPaddingTop?: import("csstype").Property.ScrollPaddingTop<string | number> | undefined;
            scrollSnapAlign?: import("csstype").Property.ScrollSnapAlign | undefined;
            scrollSnapMarginBottom?: import("csstype").Property.ScrollMarginBottom<string | number> | undefined;
            scrollSnapMarginLeft?: import("csstype").Property.ScrollMarginLeft<string | number> | undefined;
            scrollSnapMarginRight?: import("csstype").Property.ScrollMarginRight<string | number> | undefined;
            scrollSnapMarginTop?: import("csstype").Property.ScrollMarginTop<string | number> | undefined;
            scrollSnapStop?: import("csstype").Property.ScrollSnapStop | undefined;
            scrollSnapType?: import("csstype").Property.ScrollSnapType | undefined;
            scrollTimelineAxis?: import("csstype").Property.ScrollTimelineAxis | undefined;
            scrollTimelineName?: import("csstype").Property.ScrollTimelineName | undefined;
            scrollbarColor?: import("csstype").Property.ScrollbarColor | undefined;
            scrollbarGutter?: import("csstype").Property.ScrollbarGutter | undefined;
            scrollbarWidth?: import("csstype").Property.ScrollbarWidth | undefined;
            shapeImageThreshold?: import("csstype").Property.ShapeImageThreshold | undefined;
            shapeMargin?: import("csstype").Property.ShapeMargin<string | number> | undefined;
            shapeOutside?: import("csstype").Property.ShapeOutside | undefined;
            tabSize?: import("csstype").Property.TabSize<string | number> | undefined;
            tableLayout?: import("csstype").Property.TableLayout | undefined;
            textAlign?: import("csstype").Property.TextAlign | undefined;
            textAlignLast?: import("csstype").Property.TextAlignLast | undefined;
            textCombineUpright?: import("csstype").Property.TextCombineUpright | undefined;
            textDecorationColor?: import("csstype").Property.TextDecorationColor | undefined;
            textDecorationLine?: import("csstype").Property.TextDecorationLine | undefined;
            textDecorationSkip?: import("csstype").Property.TextDecorationSkip | undefined;
            textDecorationSkipInk?: import("csstype").Property.TextDecorationSkipInk | undefined;
            textDecorationStyle?: import("csstype").Property.TextDecorationStyle | undefined;
            textDecorationThickness?: import("csstype").Property.TextDecorationThickness<string | number> | undefined;
            textEmphasisColor?: import("csstype").Property.TextEmphasisColor | undefined;
            textEmphasisPosition?: import("csstype").Property.TextEmphasisPosition | undefined;
            textEmphasisStyle?: import("csstype").Property.TextEmphasisStyle | undefined;
            textIndent?: import("csstype").Property.TextIndent<string | number> | undefined;
            textJustify?: import("csstype").Property.TextJustify | undefined;
            textOrientation?: import("csstype").Property.TextOrientation | undefined;
            textOverflow?: import("csstype").Property.TextOverflow | undefined;
            textRendering?: import("csstype").Property.TextRendering | undefined;
            textShadow?: import("csstype").Property.TextShadow | undefined;
            textSizeAdjust?: import("csstype").Property.TextSizeAdjust | undefined;
            textTransform?: import("csstype").Property.TextTransform | undefined;
            textUnderlineOffset?: import("csstype").Property.TextUnderlineOffset<string | number> | undefined;
            textUnderlinePosition?: import("csstype").Property.TextUnderlinePosition | undefined;
            textWrap?: import("csstype").Property.TextWrap | undefined;
            timelineScope?: import("csstype").Property.TimelineScope | undefined;
            top?: import("csstype").Property.Top<string | number> | undefined;
            touchAction?: import("csstype").Property.TouchAction | undefined;
            transform?: import("csstype").Property.Transform | undefined;
            transformBox?: import("csstype").Property.TransformBox | undefined;
            transformOrigin?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            transformStyle?: import("csstype").Property.TransformStyle | undefined;
            transitionBehavior?: import("csstype").Property.TransitionBehavior | undefined;
            transitionDelay?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            transitionDuration?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            transitionProperty?: import("csstype").Property.TransitionProperty | undefined;
            transitionTimingFunction?: import("csstype").Property.TransitionTimingFunction | undefined;
            translate?: import("csstype").Property.Translate<string | number> | undefined;
            unicodeBidi?: import("csstype").Property.UnicodeBidi | undefined;
            userSelect?: import("csstype").Property.UserSelect | undefined;
            verticalAlign?: import("csstype").Property.VerticalAlign<string | number> | undefined;
            viewTimelineAxis?: import("csstype").Property.ViewTimelineAxis | undefined;
            viewTimelineInset?: import("csstype").Property.ViewTimelineInset<string | number> | undefined;
            viewTimelineName?: import("csstype").Property.ViewTimelineName | undefined;
            viewTransitionName?: import("csstype").Property.ViewTransitionName | undefined;
            visibility?: import("csstype").Property.Visibility | undefined;
            whiteSpace?: import("csstype").Property.WhiteSpace | undefined;
            whiteSpaceCollapse?: import("csstype").Property.WhiteSpaceCollapse | undefined;
            whiteSpaceTrim?: import("csstype").Property.WhiteSpaceTrim | undefined;
            widows?: import("csstype").Property.Widows | undefined;
            width?: import("csstype").Property.Width<string | number> | undefined;
            willChange?: import("csstype").Property.WillChange | undefined;
            wordBreak?: import("csstype").Property.WordBreak | undefined;
            wordSpacing?: import("csstype").Property.WordSpacing<string | number> | undefined;
            wordWrap?: import("csstype").Property.WordWrap | undefined;
            writingMode?: import("csstype").Property.WritingMode | undefined;
            zIndex?: import("csstype").Property.ZIndex | undefined;
            zoom?: import("csstype").Property.Zoom | undefined;
            all?: import('csstype').Globals | undefined;
            animation?: import("csstype").Property.Animation<string & {}> | undefined;
            animationRange?: import("csstype").Property.AnimationRange<string | number> | undefined;
            background?: import("csstype").Property.Background<string | number> | undefined;
            backgroundPosition?: import("csstype").Property.BackgroundPosition<string | number> | undefined;
            border?: import("csstype").Property.Border<string | number> | undefined;
            borderBlock?: import("csstype").Property.BorderBlock<string | number> | undefined;
            borderBlockEnd?: import("csstype").Property.BorderBlockEnd<string | number> | undefined;
            borderBlockStart?: import("csstype").Property.BorderBlockStart<string | number> | undefined;
            borderBottom?: import("csstype").Property.BorderBottom<string | number> | undefined;
            borderColor?: import("csstype").Property.BorderColor | undefined;
            borderImage?: import("csstype").Property.BorderImage | undefined;
            borderInline?: import("csstype").Property.BorderInline<string | number> | undefined;
            borderInlineEnd?: import("csstype").Property.BorderInlineEnd<string | number> | undefined;
            borderInlineStart?: import("csstype").Property.BorderInlineStart<string | number> | undefined;
            borderLeft?: import("csstype").Property.BorderLeft<string | number> | undefined;
            borderRadius?: import("csstype").Property.BorderRadius<string | number> | undefined;
            borderRight?: import("csstype").Property.BorderRight<string | number> | undefined;
            borderStyle?: import("csstype").Property.BorderStyle | undefined;
            borderTop?: import("csstype").Property.BorderTop<string | number> | undefined;
            borderWidth?: import("csstype").Property.BorderWidth<string | number> | undefined;
            caret?: import("csstype").Property.Caret | undefined;
            columnRule?: import("csstype").Property.ColumnRule<string | number> | undefined;
            columns?: import("csstype").Property.Columns<string | number> | undefined;
            containIntrinsicSize?: import("csstype").Property.ContainIntrinsicSize<string | number> | undefined;
            container?: import("csstype").Property.Container | undefined;
            flex?: import("csstype").Property.Flex<string | number> | undefined;
            flexFlow?: import("csstype").Property.FlexFlow | undefined;
            font?: import("csstype").Property.Font | undefined;
            gap?: import("csstype").Property.Gap<string | number> | undefined;
            grid?: import("csstype").Property.Grid | undefined;
            gridArea?: import("csstype").Property.GridArea | undefined;
            gridColumn?: import("csstype").Property.GridColumn | undefined;
            gridRow?: import("csstype").Property.GridRow | undefined;
            gridTemplate?: import("csstype").Property.GridTemplate | undefined;
            inset?: import("csstype").Property.Inset<string | number> | undefined;
            insetBlock?: import("csstype").Property.InsetBlock<string | number> | undefined;
            insetInline?: import("csstype").Property.InsetInline<string | number> | undefined;
            lineClamp?: import("csstype").Property.LineClamp | undefined;
            listStyle?: import("csstype").Property.ListStyle | undefined;
            margin?: import("csstype").Property.Margin<string | number> | undefined;
            marginBlock?: import("csstype").Property.MarginBlock<string | number> | undefined;
            marginInline?: import("csstype").Property.MarginInline<string | number> | undefined;
            mask?: import("csstype").Property.Mask<string | number> | undefined;
            maskBorder?: import("csstype").Property.MaskBorder | undefined;
            motion?: import("csstype").Property.Offset<string | number> | undefined;
            offset?: import("csstype").Property.Offset<string | number> | undefined;
            outline?: import("csstype").Property.Outline<string | number> | undefined;
            overflow?: import("csstype").Property.Overflow | undefined;
            overscrollBehavior?: import("csstype").Property.OverscrollBehavior | undefined;
            padding?: import("csstype").Property.Padding<string | number> | undefined;
            paddingBlock?: import("csstype").Property.PaddingBlock<string | number> | undefined;
            paddingInline?: import("csstype").Property.PaddingInline<string | number> | undefined;
            placeContent?: import("csstype").Property.PlaceContent | undefined;
            placeItems?: import("csstype").Property.PlaceItems | undefined;
            placeSelf?: import("csstype").Property.PlaceSelf | undefined;
            scrollMargin?: import("csstype").Property.ScrollMargin<string | number> | undefined;
            scrollMarginBlock?: import("csstype").Property.ScrollMarginBlock<string | number> | undefined;
            scrollMarginInline?: import("csstype").Property.ScrollMarginInline<string | number> | undefined;
            scrollPadding?: import("csstype").Property.ScrollPadding<string | number> | undefined;
            scrollPaddingBlock?: import("csstype").Property.ScrollPaddingBlock<string | number> | undefined;
            scrollPaddingInline?: import("csstype").Property.ScrollPaddingInline<string | number> | undefined;
            scrollSnapMargin?: import("csstype").Property.ScrollMargin<string | number> | undefined;
            scrollTimeline?: import("csstype").Property.ScrollTimeline | undefined;
            textDecoration?: import("csstype").Property.TextDecoration<string | number> | undefined;
            textEmphasis?: import("csstype").Property.TextEmphasis | undefined;
            transition?: import("csstype").Property.Transition<string & {}> | undefined;
            viewTimeline?: import("csstype").Property.ViewTimeline | undefined;
            MozAnimationDelay?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            MozAnimationDirection?: import("csstype").Property.AnimationDirection | undefined;
            MozAnimationDuration?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            MozAnimationFillMode?: import("csstype").Property.AnimationFillMode | undefined;
            MozAnimationIterationCount?: import("csstype").Property.AnimationIterationCount | undefined;
            MozAnimationName?: import("csstype").Property.AnimationName | undefined;
            MozAnimationPlayState?: import("csstype").Property.AnimationPlayState | undefined;
            MozAnimationTimingFunction?: import("csstype").Property.AnimationTimingFunction | undefined;
            MozAppearance?: import("csstype").Property.MozAppearance | undefined;
            MozBinding?: import("csstype").Property.MozBinding | undefined;
            MozBorderBottomColors?: import("csstype").Property.MozBorderBottomColors | undefined;
            MozBorderEndColor?: import("csstype").Property.BorderInlineEndColor | undefined;
            MozBorderEndStyle?: import("csstype").Property.BorderInlineEndStyle | undefined;
            MozBorderEndWidth?: import("csstype").Property.BorderInlineEndWidth<string | number> | undefined;
            MozBorderLeftColors?: import("csstype").Property.MozBorderLeftColors | undefined;
            MozBorderRightColors?: import("csstype").Property.MozBorderRightColors | undefined;
            MozBorderStartColor?: import("csstype").Property.BorderInlineStartColor | undefined;
            MozBorderStartStyle?: import("csstype").Property.BorderInlineStartStyle | undefined;
            MozBorderTopColors?: import("csstype").Property.MozBorderTopColors | undefined;
            MozBoxSizing?: import("csstype").Property.BoxSizing | undefined;
            MozColumnCount?: import("csstype").Property.ColumnCount | undefined;
            MozColumnFill?: import("csstype").Property.ColumnFill | undefined;
            MozColumnRuleColor?: import("csstype").Property.ColumnRuleColor | undefined;
            MozColumnRuleStyle?: import("csstype").Property.ColumnRuleStyle | undefined;
            MozColumnRuleWidth?: import("csstype").Property.ColumnRuleWidth<string | number> | undefined;
            MozColumnWidth?: import("csstype").Property.ColumnWidth<string | number> | undefined;
            MozContextProperties?: import("csstype").Property.MozContextProperties | undefined;
            MozFontFeatureSettings?: import("csstype").Property.FontFeatureSettings | undefined;
            MozFontLanguageOverride?: import("csstype").Property.FontLanguageOverride | undefined;
            MozHyphens?: import("csstype").Property.Hyphens | undefined;
            MozImageRegion?: import("csstype").Property.MozImageRegion | undefined;
            MozMarginEnd?: import("csstype").Property.MarginInlineEnd<string | number> | undefined;
            MozMarginStart?: import("csstype").Property.MarginInlineStart<string | number> | undefined;
            MozOrient?: import("csstype").Property.MozOrient | undefined;
            MozOsxFontSmoothing?: import("csstype").Property.FontSmooth<string | number> | undefined;
            MozOutlineRadiusBottomleft?: import("csstype").Property.MozOutlineRadiusBottomleft<string | number> | undefined;
            MozOutlineRadiusBottomright?: import("csstype").Property.MozOutlineRadiusBottomright<string | number> | undefined;
            MozOutlineRadiusTopleft?: import("csstype").Property.MozOutlineRadiusTopleft<string | number> | undefined;
            MozOutlineRadiusTopright?: import("csstype").Property.MozOutlineRadiusTopright<string | number> | undefined;
            MozPaddingEnd?: import("csstype").Property.PaddingInlineEnd<string | number> | undefined;
            MozPaddingStart?: import("csstype").Property.PaddingInlineStart<string | number> | undefined;
            MozStackSizing?: import("csstype").Property.MozStackSizing | undefined;
            MozTabSize?: import("csstype").Property.TabSize<string | number> | undefined;
            MozTextBlink?: import("csstype").Property.MozTextBlink | undefined;
            MozTextSizeAdjust?: import("csstype").Property.TextSizeAdjust | undefined;
            MozUserFocus?: import("csstype").Property.MozUserFocus | undefined;
            MozUserModify?: import("csstype").Property.MozUserModify | undefined;
            MozUserSelect?: import("csstype").Property.UserSelect | undefined;
            MozWindowDragging?: import("csstype").Property.MozWindowDragging | undefined;
            MozWindowShadow?: import("csstype").Property.MozWindowShadow | undefined;
            msAccelerator?: import("csstype").Property.MsAccelerator | undefined;
            msBlockProgression?: import("csstype").Property.MsBlockProgression | undefined;
            msContentZoomChaining?: import("csstype").Property.MsContentZoomChaining | undefined;
            msContentZoomLimitMax?: import("csstype").Property.MsContentZoomLimitMax | undefined;
            msContentZoomLimitMin?: import("csstype").Property.MsContentZoomLimitMin | undefined;
            msContentZoomSnapPoints?: import("csstype").Property.MsContentZoomSnapPoints | undefined;
            msContentZoomSnapType?: import("csstype").Property.MsContentZoomSnapType | undefined;
            msContentZooming?: import("csstype").Property.MsContentZooming | undefined;
            msFilter?: import("csstype").Property.MsFilter | undefined;
            msFlexDirection?: import("csstype").Property.FlexDirection | undefined;
            msFlexPositive?: import("csstype").Property.FlexGrow | undefined;
            msFlowFrom?: import("csstype").Property.MsFlowFrom | undefined;
            msFlowInto?: import("csstype").Property.MsFlowInto | undefined;
            msGridColumns?: import("csstype").Property.MsGridColumns<string | number> | undefined;
            msGridRows?: import("csstype").Property.MsGridRows<string | number> | undefined;
            msHighContrastAdjust?: import("csstype").Property.MsHighContrastAdjust | undefined;
            msHyphenateLimitChars?: import("csstype").Property.MsHyphenateLimitChars | undefined;
            msHyphenateLimitLines?: import("csstype").Property.MsHyphenateLimitLines | undefined;
            msHyphenateLimitZone?: import("csstype").Property.MsHyphenateLimitZone<string | number> | undefined;
            msHyphens?: import("csstype").Property.Hyphens | undefined;
            msImeAlign?: import("csstype").Property.MsImeAlign | undefined;
            msLineBreak?: import("csstype").Property.LineBreak | undefined;
            msOrder?: import("csstype").Property.Order | undefined;
            msOverflowStyle?: import("csstype").Property.MsOverflowStyle | undefined;
            msOverflowX?: import("csstype").Property.OverflowX | undefined;
            msOverflowY?: import("csstype").Property.OverflowY | undefined;
            msScrollChaining?: import("csstype").Property.MsScrollChaining | undefined;
            msScrollLimitXMax?: import("csstype").Property.MsScrollLimitXMax<string | number> | undefined;
            msScrollLimitXMin?: import("csstype").Property.MsScrollLimitXMin<string | number> | undefined;
            msScrollLimitYMax?: import("csstype").Property.MsScrollLimitYMax<string | number> | undefined;
            msScrollLimitYMin?: import("csstype").Property.MsScrollLimitYMin<string | number> | undefined;
            msScrollRails?: import("csstype").Property.MsScrollRails | undefined;
            msScrollSnapPointsX?: import("csstype").Property.MsScrollSnapPointsX | undefined;
            msScrollSnapPointsY?: import("csstype").Property.MsScrollSnapPointsY | undefined;
            msScrollSnapType?: import("csstype").Property.MsScrollSnapType | undefined;
            msScrollTranslation?: import("csstype").Property.MsScrollTranslation | undefined;
            msScrollbar3dlightColor?: import("csstype").Property.MsScrollbar3dlightColor | undefined;
            msScrollbarArrowColor?: import("csstype").Property.MsScrollbarArrowColor | undefined;
            msScrollbarBaseColor?: import("csstype").Property.MsScrollbarBaseColor | undefined;
            msScrollbarDarkshadowColor?: import("csstype").Property.MsScrollbarDarkshadowColor | undefined;
            msScrollbarFaceColor?: import("csstype").Property.MsScrollbarFaceColor | undefined;
            msScrollbarHighlightColor?: import("csstype").Property.MsScrollbarHighlightColor | undefined;
            msScrollbarShadowColor?: import("csstype").Property.MsScrollbarShadowColor | undefined;
            msScrollbarTrackColor?: import("csstype").Property.MsScrollbarTrackColor | undefined;
            msTextAutospace?: import("csstype").Property.MsTextAutospace | undefined;
            msTextCombineHorizontal?: import("csstype").Property.TextCombineUpright | undefined;
            msTextOverflow?: import("csstype").Property.TextOverflow | undefined;
            msTouchAction?: import("csstype").Property.TouchAction | undefined;
            msTouchSelect?: import("csstype").Property.MsTouchSelect | undefined;
            msTransform?: import("csstype").Property.Transform | undefined;
            msTransformOrigin?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            msTransitionDelay?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            msTransitionDuration?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            msTransitionProperty?: import("csstype").Property.TransitionProperty | undefined;
            msTransitionTimingFunction?: import("csstype").Property.TransitionTimingFunction | undefined;
            msUserSelect?: import("csstype").Property.MsUserSelect | undefined;
            msWordBreak?: import("csstype").Property.WordBreak | undefined;
            msWrapFlow?: import("csstype").Property.MsWrapFlow | undefined;
            msWrapMargin?: import("csstype").Property.MsWrapMargin<string | number> | undefined;
            msWrapThrough?: import("csstype").Property.MsWrapThrough | undefined;
            msWritingMode?: import("csstype").Property.WritingMode | undefined;
            WebkitAlignContent?: import("csstype").Property.AlignContent | undefined;
            WebkitAlignItems?: import("csstype").Property.AlignItems | undefined;
            WebkitAlignSelf?: import("csstype").Property.AlignSelf | undefined;
            WebkitAnimationDelay?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            WebkitAnimationDirection?: import("csstype").Property.AnimationDirection | undefined;
            WebkitAnimationDuration?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            WebkitAnimationFillMode?: import("csstype").Property.AnimationFillMode | undefined;
            WebkitAnimationIterationCount?: import("csstype").Property.AnimationIterationCount | undefined;
            WebkitAnimationName?: import("csstype").Property.AnimationName | undefined;
            WebkitAnimationPlayState?: import("csstype").Property.AnimationPlayState | undefined;
            WebkitAnimationTimingFunction?: import("csstype").Property.AnimationTimingFunction | undefined;
            WebkitAppearance?: import("csstype").Property.WebkitAppearance | undefined;
            WebkitBackdropFilter?: import("csstype").Property.BackdropFilter | undefined;
            WebkitBackfaceVisibility?: import("csstype").Property.BackfaceVisibility | undefined;
            WebkitBackgroundClip?: import("csstype").Property.BackgroundClip | undefined;
            WebkitBackgroundOrigin?: import("csstype").Property.BackgroundOrigin | undefined;
            WebkitBackgroundSize?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            WebkitBorderBeforeColor?: import("csstype").Property.WebkitBorderBeforeColor | undefined;
            WebkitBorderBeforeStyle?: import("csstype").Property.WebkitBorderBeforeStyle | undefined;
            WebkitBorderBeforeWidth?: import("csstype").Property.WebkitBorderBeforeWidth<string | number> | undefined;
            WebkitBorderBottomLeftRadius?: import("csstype").Property.BorderBottomLeftRadius<string | number> | undefined;
            WebkitBorderBottomRightRadius?: import("csstype").Property.BorderBottomRightRadius<string | number> | undefined;
            WebkitBorderImageSlice?: import("csstype").Property.BorderImageSlice | undefined;
            WebkitBorderTopLeftRadius?: import("csstype").Property.BorderTopLeftRadius<string | number> | undefined;
            WebkitBorderTopRightRadius?: import("csstype").Property.BorderTopRightRadius<string | number> | undefined;
            WebkitBoxDecorationBreak?: import("csstype").Property.BoxDecorationBreak | undefined;
            WebkitBoxReflect?: import("csstype").Property.WebkitBoxReflect<string | number> | undefined;
            WebkitBoxShadow?: import("csstype").Property.BoxShadow | undefined;
            WebkitBoxSizing?: import("csstype").Property.BoxSizing | undefined;
            WebkitClipPath?: import("csstype").Property.ClipPath | undefined;
            WebkitColumnCount?: import("csstype").Property.ColumnCount | undefined;
            WebkitColumnFill?: import("csstype").Property.ColumnFill | undefined;
            WebkitColumnRuleColor?: import("csstype").Property.ColumnRuleColor | undefined;
            WebkitColumnRuleStyle?: import("csstype").Property.ColumnRuleStyle | undefined;
            WebkitColumnRuleWidth?: import("csstype").Property.ColumnRuleWidth<string | number> | undefined;
            WebkitColumnSpan?: import("csstype").Property.ColumnSpan | undefined;
            WebkitColumnWidth?: import("csstype").Property.ColumnWidth<string | number> | undefined;
            WebkitFilter?: import("csstype").Property.Filter | undefined;
            WebkitFlexBasis?: import("csstype").Property.FlexBasis<string | number> | undefined;
            WebkitFlexDirection?: import("csstype").Property.FlexDirection | undefined;
            WebkitFlexGrow?: import("csstype").Property.FlexGrow | undefined;
            WebkitFlexShrink?: import("csstype").Property.FlexShrink | undefined;
            WebkitFlexWrap?: import("csstype").Property.FlexWrap | undefined;
            WebkitFontFeatureSettings?: import("csstype").Property.FontFeatureSettings | undefined;
            WebkitFontKerning?: import("csstype").Property.FontKerning | undefined;
            WebkitFontSmoothing?: import("csstype").Property.FontSmooth<string | number> | undefined;
            WebkitFontVariantLigatures?: import("csstype").Property.FontVariantLigatures | undefined;
            WebkitHyphenateCharacter?: import("csstype").Property.HyphenateCharacter | undefined;
            WebkitHyphens?: import("csstype").Property.Hyphens | undefined;
            WebkitInitialLetter?: import("csstype").Property.InitialLetter | undefined;
            WebkitJustifyContent?: import("csstype").Property.JustifyContent | undefined;
            WebkitLineBreak?: import("csstype").Property.LineBreak | undefined;
            WebkitLineClamp?: import("csstype").Property.WebkitLineClamp | undefined;
            WebkitMarginEnd?: import("csstype").Property.MarginInlineEnd<string | number> | undefined;
            WebkitMarginStart?: import("csstype").Property.MarginInlineStart<string | number> | undefined;
            WebkitMaskAttachment?: import("csstype").Property.WebkitMaskAttachment | undefined;
            WebkitMaskBoxImageOutset?: import("csstype").Property.MaskBorderOutset<string | number> | undefined;
            WebkitMaskBoxImageRepeat?: import("csstype").Property.MaskBorderRepeat | undefined;
            WebkitMaskBoxImageSlice?: import("csstype").Property.MaskBorderSlice | undefined;
            WebkitMaskBoxImageSource?: import("csstype").Property.MaskBorderSource | undefined;
            WebkitMaskBoxImageWidth?: import("csstype").Property.MaskBorderWidth<string | number> | undefined;
            WebkitMaskClip?: import("csstype").Property.WebkitMaskClip | undefined;
            WebkitMaskComposite?: import("csstype").Property.WebkitMaskComposite | undefined;
            WebkitMaskImage?: import("csstype").Property.WebkitMaskImage | undefined;
            WebkitMaskOrigin?: import("csstype").Property.WebkitMaskOrigin | undefined;
            WebkitMaskPosition?: import("csstype").Property.WebkitMaskPosition<string | number> | undefined;
            WebkitMaskPositionX?: import("csstype").Property.WebkitMaskPositionX<string | number> | undefined;
            WebkitMaskPositionY?: import("csstype").Property.WebkitMaskPositionY<string | number> | undefined;
            WebkitMaskRepeat?: import("csstype").Property.WebkitMaskRepeat | undefined;
            WebkitMaskRepeatX?: import("csstype").Property.WebkitMaskRepeatX | undefined;
            WebkitMaskRepeatY?: import("csstype").Property.WebkitMaskRepeatY | undefined;
            WebkitMaskSize?: import("csstype").Property.WebkitMaskSize<string | number> | undefined;
            WebkitMaxInlineSize?: import("csstype").Property.MaxInlineSize<string | number> | undefined;
            WebkitOrder?: import("csstype").Property.Order | undefined;
            WebkitOverflowScrolling?: import("csstype").Property.WebkitOverflowScrolling | undefined;
            WebkitPaddingEnd?: import("csstype").Property.PaddingInlineEnd<string | number> | undefined;
            WebkitPaddingStart?: import("csstype").Property.PaddingInlineStart<string | number> | undefined;
            WebkitPerspective?: import("csstype").Property.Perspective<string | number> | undefined;
            WebkitPerspectiveOrigin?: import("csstype").Property.PerspectiveOrigin<string | number> | undefined;
            WebkitPrintColorAdjust?: import("csstype").Property.PrintColorAdjust | undefined;
            WebkitRubyPosition?: import("csstype").Property.RubyPosition | undefined;
            WebkitScrollSnapType?: import("csstype").Property.ScrollSnapType | undefined;
            WebkitShapeMargin?: import("csstype").Property.ShapeMargin<string | number> | undefined;
            WebkitTapHighlightColor?: import("csstype").Property.WebkitTapHighlightColor | undefined;
            WebkitTextCombine?: import("csstype").Property.TextCombineUpright | undefined;
            WebkitTextDecorationColor?: import("csstype").Property.TextDecorationColor | undefined;
            WebkitTextDecorationLine?: import("csstype").Property.TextDecorationLine | undefined;
            WebkitTextDecorationSkip?: import("csstype").Property.TextDecorationSkip | undefined;
            WebkitTextDecorationStyle?: import("csstype").Property.TextDecorationStyle | undefined;
            WebkitTextEmphasisColor?: import("csstype").Property.TextEmphasisColor | undefined;
            WebkitTextEmphasisPosition?: import("csstype").Property.TextEmphasisPosition | undefined;
            WebkitTextEmphasisStyle?: import("csstype").Property.TextEmphasisStyle | undefined;
            WebkitTextFillColor?: import("csstype").Property.WebkitTextFillColor | undefined;
            WebkitTextOrientation?: import("csstype").Property.TextOrientation | undefined;
            WebkitTextSizeAdjust?: import("csstype").Property.TextSizeAdjust | undefined;
            WebkitTextStrokeColor?: import("csstype").Property.WebkitTextStrokeColor | undefined;
            WebkitTextStrokeWidth?: import("csstype").Property.WebkitTextStrokeWidth<string | number> | undefined;
            WebkitTextUnderlinePosition?: import("csstype").Property.TextUnderlinePosition | undefined;
            WebkitTouchCallout?: import("csstype").Property.WebkitTouchCallout | undefined;
            WebkitTransform?: import("csstype").Property.Transform | undefined;
            WebkitTransformOrigin?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            WebkitTransformStyle?: import("csstype").Property.TransformStyle | undefined;
            WebkitTransitionDelay?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            WebkitTransitionDuration?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            WebkitTransitionProperty?: import("csstype").Property.TransitionProperty | undefined;
            WebkitTransitionTimingFunction?: import("csstype").Property.TransitionTimingFunction | undefined;
            WebkitUserModify?: import("csstype").Property.WebkitUserModify | undefined;
            WebkitUserSelect?: import("csstype").Property.UserSelect | undefined;
            WebkitWritingMode?: import("csstype").Property.WritingMode | undefined;
            MozAnimation?: import("csstype").Property.Animation<string & {}> | undefined;
            MozBorderImage?: import("csstype").Property.BorderImage | undefined;
            MozColumnRule?: import("csstype").Property.ColumnRule<string | number> | undefined;
            MozColumns?: import("csstype").Property.Columns<string | number> | undefined;
            MozOutlineRadius?: import("csstype").Property.MozOutlineRadius<string | number> | undefined;
            msContentZoomLimit?: import("csstype").Property.MsContentZoomLimit | undefined;
            msContentZoomSnap?: import("csstype").Property.MsContentZoomSnap | undefined;
            msFlex?: import("csstype").Property.Flex<string | number> | undefined;
            msScrollLimit?: import("csstype").Property.MsScrollLimit | undefined;
            msScrollSnapX?: import("csstype").Property.MsScrollSnapX | undefined;
            msScrollSnapY?: import("csstype").Property.MsScrollSnapY | undefined;
            msTransition?: import("csstype").Property.Transition<string & {}> | undefined;
            WebkitAnimation?: import("csstype").Property.Animation<string & {}> | undefined;
            WebkitBorderBefore?: import("csstype").Property.WebkitBorderBefore<string | number> | undefined;
            WebkitBorderImage?: import("csstype").Property.BorderImage | undefined;
            WebkitBorderRadius?: import("csstype").Property.BorderRadius<string | number> | undefined;
            WebkitColumnRule?: import("csstype").Property.ColumnRule<string | number> | undefined;
            WebkitColumns?: import("csstype").Property.Columns<string | number> | undefined;
            WebkitFlex?: import("csstype").Property.Flex<string | number> | undefined;
            WebkitFlexFlow?: import("csstype").Property.FlexFlow | undefined;
            WebkitMask?: import("csstype").Property.WebkitMask<string | number> | undefined;
            WebkitMaskBoxImage?: import("csstype").Property.MaskBorder | undefined;
            WebkitTextEmphasis?: import("csstype").Property.TextEmphasis | undefined;
            WebkitTextStroke?: import("csstype").Property.WebkitTextStroke<string | number> | undefined;
            WebkitTransition?: import("csstype").Property.Transition<string & {}> | undefined;
            azimuth?: import("csstype").Property.Azimuth | undefined;
            boxAlign?: import("csstype").Property.BoxAlign | undefined;
            boxDirection?: import("csstype").Property.BoxDirection | undefined;
            boxFlex?: import("csstype").Property.BoxFlex | undefined;
            boxFlexGroup?: import("csstype").Property.BoxFlexGroup | undefined;
            boxLines?: import("csstype").Property.BoxLines | undefined;
            boxOrdinalGroup?: import("csstype").Property.BoxOrdinalGroup | undefined;
            boxOrient?: import("csstype").Property.BoxOrient | undefined;
            boxPack?: import("csstype").Property.BoxPack | undefined;
            clip?: import("csstype").Property.Clip | undefined;
            gridColumnGap?: import("csstype").Property.GridColumnGap<string | number> | undefined;
            gridGap?: import("csstype").Property.GridGap<string | number> | undefined;
            gridRowGap?: import("csstype").Property.GridRowGap<string | number> | undefined;
            imeMode?: import("csstype").Property.ImeMode | undefined;
            offsetBlock?: import("csstype").Property.InsetBlock<string | number> | undefined;
            offsetBlockEnd?: import("csstype").Property.InsetBlockEnd<string | number> | undefined;
            offsetBlockStart?: import("csstype").Property.InsetBlockStart<string | number> | undefined;
            offsetInline?: import("csstype").Property.InsetInline<string | number> | undefined;
            offsetInlineEnd?: import("csstype").Property.InsetInlineEnd<string | number> | undefined;
            offsetInlineStart?: import("csstype").Property.InsetInlineStart<string | number> | undefined;
            scrollSnapCoordinate?: import("csstype").Property.ScrollSnapCoordinate<string | number> | undefined;
            scrollSnapDestination?: import("csstype").Property.ScrollSnapDestination<string | number> | undefined;
            scrollSnapPointsX?: import("csstype").Property.ScrollSnapPointsX | undefined;
            scrollSnapPointsY?: import("csstype").Property.ScrollSnapPointsY | undefined;
            scrollSnapTypeX?: import("csstype").Property.ScrollSnapTypeX | undefined;
            scrollSnapTypeY?: import("csstype").Property.ScrollSnapTypeY | undefined;
            KhtmlBoxAlign?: import("csstype").Property.BoxAlign | undefined;
            KhtmlBoxDirection?: import("csstype").Property.BoxDirection | undefined;
            KhtmlBoxFlex?: import("csstype").Property.BoxFlex | undefined;
            KhtmlBoxFlexGroup?: import("csstype").Property.BoxFlexGroup | undefined;
            KhtmlBoxLines?: import("csstype").Property.BoxLines | undefined;
            KhtmlBoxOrdinalGroup?: import("csstype").Property.BoxOrdinalGroup | undefined;
            KhtmlBoxOrient?: import("csstype").Property.BoxOrient | undefined;
            KhtmlBoxPack?: import("csstype").Property.BoxPack | undefined;
            KhtmlLineBreak?: import("csstype").Property.LineBreak | undefined;
            KhtmlOpacity?: import("csstype").Property.Opacity | undefined;
            KhtmlUserSelect?: import("csstype").Property.UserSelect | undefined;
            MozBackfaceVisibility?: import("csstype").Property.BackfaceVisibility | undefined;
            MozBackgroundClip?: import("csstype").Property.BackgroundClip | undefined;
            MozBackgroundInlinePolicy?: import("csstype").Property.BoxDecorationBreak | undefined;
            MozBackgroundOrigin?: import("csstype").Property.BackgroundOrigin | undefined;
            MozBackgroundSize?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            MozBorderRadius?: import("csstype").Property.BorderRadius<string | number> | undefined;
            MozBorderRadiusBottomleft?: import("csstype").Property.BorderBottomLeftRadius<string | number> | undefined;
            MozBorderRadiusBottomright?: import("csstype").Property.BorderBottomRightRadius<string | number> | undefined;
            MozBorderRadiusTopleft?: import("csstype").Property.BorderTopLeftRadius<string | number> | undefined;
            MozBorderRadiusTopright?: import("csstype").Property.BorderTopRightRadius<string | number> | undefined;
            MozBoxAlign?: import("csstype").Property.BoxAlign | undefined;
            MozBoxDirection?: import("csstype").Property.BoxDirection | undefined;
            MozBoxFlex?: import("csstype").Property.BoxFlex | undefined;
            MozBoxOrdinalGroup?: import("csstype").Property.BoxOrdinalGroup | undefined;
            MozBoxOrient?: import("csstype").Property.BoxOrient | undefined;
            MozBoxPack?: import("csstype").Property.BoxPack | undefined;
            MozBoxShadow?: import("csstype").Property.BoxShadow | undefined;
            MozFloatEdge?: import("csstype").Property.MozFloatEdge | undefined;
            MozForceBrokenImageIcon?: import("csstype").Property.MozForceBrokenImageIcon | undefined;
            MozOpacity?: import("csstype").Property.Opacity | undefined;
            MozOutline?: import("csstype").Property.Outline<string | number> | undefined;
            MozOutlineColor?: import("csstype").Property.OutlineColor | undefined;
            MozOutlineStyle?: import("csstype").Property.OutlineStyle | undefined;
            MozOutlineWidth?: import("csstype").Property.OutlineWidth<string | number> | undefined;
            MozPerspective?: import("csstype").Property.Perspective<string | number> | undefined;
            MozPerspectiveOrigin?: import("csstype").Property.PerspectiveOrigin<string | number> | undefined;
            MozTextAlignLast?: import("csstype").Property.TextAlignLast | undefined;
            MozTextDecorationColor?: import("csstype").Property.TextDecorationColor | undefined;
            MozTextDecorationLine?: import("csstype").Property.TextDecorationLine | undefined;
            MozTextDecorationStyle?: import("csstype").Property.TextDecorationStyle | undefined;
            MozTransform?: import("csstype").Property.Transform | undefined;
            MozTransformOrigin?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            MozTransformStyle?: import("csstype").Property.TransformStyle | undefined;
            MozTransition?: import("csstype").Property.Transition<string & {}> | undefined;
            MozTransitionDelay?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            MozTransitionDuration?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            MozTransitionProperty?: import("csstype").Property.TransitionProperty | undefined;
            MozTransitionTimingFunction?: import("csstype").Property.TransitionTimingFunction | undefined;
            MozUserInput?: import("csstype").Property.MozUserInput | undefined;
            msImeMode?: import("csstype").Property.ImeMode | undefined;
            OAnimation?: import("csstype").Property.Animation<string & {}> | undefined;
            OAnimationDelay?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            OAnimationDirection?: import("csstype").Property.AnimationDirection | undefined;
            OAnimationDuration?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            OAnimationFillMode?: import("csstype").Property.AnimationFillMode | undefined;
            OAnimationIterationCount?: import("csstype").Property.AnimationIterationCount | undefined;
            OAnimationName?: import("csstype").Property.AnimationName | undefined;
            OAnimationPlayState?: import("csstype").Property.AnimationPlayState | undefined;
            OAnimationTimingFunction?: import("csstype").Property.AnimationTimingFunction | undefined;
            OBackgroundSize?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            OBorderImage?: import("csstype").Property.BorderImage | undefined;
            OObjectFit?: import("csstype").Property.ObjectFit | undefined;
            OObjectPosition?: import("csstype").Property.ObjectPosition<string | number> | undefined;
            OTabSize?: import("csstype").Property.TabSize<string | number> | undefined;
            OTextOverflow?: import("csstype").Property.TextOverflow | undefined;
            OTransform?: import("csstype").Property.Transform | undefined;
            OTransformOrigin?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            OTransition?: import("csstype").Property.Transition<string & {}> | undefined;
            OTransitionDelay?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            OTransitionDuration?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            OTransitionProperty?: import("csstype").Property.TransitionProperty | undefined;
            OTransitionTimingFunction?: import("csstype").Property.TransitionTimingFunction | undefined;
            WebkitBoxAlign?: import("csstype").Property.BoxAlign | undefined;
            WebkitBoxDirection?: import("csstype").Property.BoxDirection | undefined;
            WebkitBoxFlex?: import("csstype").Property.BoxFlex | undefined;
            WebkitBoxFlexGroup?: import("csstype").Property.BoxFlexGroup | undefined;
            WebkitBoxLines?: import("csstype").Property.BoxLines | undefined;
            WebkitBoxOrdinalGroup?: import("csstype").Property.BoxOrdinalGroup | undefined;
            WebkitBoxOrient?: import("csstype").Property.BoxOrient | undefined;
            WebkitBoxPack?: import("csstype").Property.BoxPack | undefined;
            alignmentBaseline?: import("csstype").Property.AlignmentBaseline | undefined;
            baselineShift?: import("csstype").Property.BaselineShift<string | number> | undefined;
            clipRule?: import("csstype").Property.ClipRule | undefined;
            colorInterpolation?: import("csstype").Property.ColorInterpolation | undefined;
            colorRendering?: import("csstype").Property.ColorRendering | undefined;
            dominantBaseline?: import("csstype").Property.DominantBaseline | undefined;
            fill?: import("csstype").Property.Fill | undefined;
            fillOpacity?: import("csstype").Property.FillOpacity | undefined;
            fillRule?: import("csstype").Property.FillRule | undefined;
            floodColor?: import("csstype").Property.FloodColor | undefined;
            floodOpacity?: import("csstype").Property.FloodOpacity | undefined;
            glyphOrientationVertical?: import("csstype").Property.GlyphOrientationVertical | undefined;
            lightingColor?: import("csstype").Property.LightingColor | undefined;
            marker?: import("csstype").Property.Marker | undefined;
            markerEnd?: import("csstype").Property.MarkerEnd | undefined;
            markerMid?: import("csstype").Property.MarkerMid | undefined;
            markerStart?: import("csstype").Property.MarkerStart | undefined;
            shapeRendering?: import("csstype").Property.ShapeRendering | undefined;
            stopColor?: import("csstype").Property.StopColor | undefined;
            stopOpacity?: import("csstype").Property.StopOpacity | undefined;
            stroke?: import("csstype").Property.Stroke | undefined;
            strokeDasharray?: import("csstype").Property.StrokeDasharray<string | number> | undefined;
            strokeDashoffset?: import("csstype").Property.StrokeDashoffset<string | number> | undefined;
            strokeLinecap?: import("csstype").Property.StrokeLinecap | undefined;
            strokeLinejoin?: import("csstype").Property.StrokeLinejoin | undefined;
            strokeMiterlimit?: import("csstype").Property.StrokeMiterlimit | undefined;
            strokeOpacity?: import("csstype").Property.StrokeOpacity | undefined;
            strokeWidth?: import("csstype").Property.StrokeWidth<string | number> | undefined;
            textAnchor?: import("csstype").Property.TextAnchor | undefined;
            vectorEffect?: import("csstype").Property.VectorEffect | undefined;
            "accent-color"?: import("csstype").Property.AccentColor | undefined;
            "align-content"?: import("csstype").Property.AlignContent | undefined;
            "align-items"?: import("csstype").Property.AlignItems | undefined;
            "align-self"?: import("csstype").Property.AlignSelf | undefined;
            "align-tracks"?: import("csstype").Property.AlignTracks | undefined;
            "animation-composition"?: import("csstype").Property.AnimationComposition | undefined;
            "animation-delay"?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            "animation-direction"?: import("csstype").Property.AnimationDirection | undefined;
            "animation-duration"?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            "animation-fill-mode"?: import("csstype").Property.AnimationFillMode | undefined;
            "animation-iteration-count"?: import("csstype").Property.AnimationIterationCount | undefined;
            "animation-name"?: import("csstype").Property.AnimationName | undefined;
            "animation-play-state"?: import("csstype").Property.AnimationPlayState | undefined;
            "animation-range-end"?: import("csstype").Property.AnimationRangeEnd<string | number> | undefined;
            "animation-range-start"?: import("csstype").Property.AnimationRangeStart<string | number> | undefined;
            "animation-timeline"?: import("csstype").Property.AnimationTimeline | undefined;
            "animation-timing-function"?: import("csstype").Property.AnimationTimingFunction | undefined;
            "aspect-ratio"?: import("csstype").Property.AspectRatio | undefined;
            "backdrop-filter"?: import("csstype").Property.BackdropFilter | undefined;
            "backface-visibility"?: import("csstype").Property.BackfaceVisibility | undefined;
            "background-attachment"?: import("csstype").Property.BackgroundAttachment | undefined;
            "background-blend-mode"?: import("csstype").Property.BackgroundBlendMode | undefined;
            "background-clip"?: import("csstype").Property.BackgroundClip | undefined;
            "background-color"?: import("csstype").Property.BackgroundColor | undefined;
            "background-image"?: import("csstype").Property.BackgroundImage | undefined;
            "background-origin"?: import("csstype").Property.BackgroundOrigin | undefined;
            "background-position-x"?: import("csstype").Property.BackgroundPositionX<string | number> | undefined;
            "background-position-y"?: import("csstype").Property.BackgroundPositionY<string | number> | undefined;
            "background-repeat"?: import("csstype").Property.BackgroundRepeat | undefined;
            "background-size"?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            "block-overflow"?: import("csstype").Property.BlockOverflow | undefined;
            "block-size"?: import("csstype").Property.BlockSize<string | number> | undefined;
            "border-block-color"?: import("csstype").Property.BorderBlockColor | undefined;
            "border-block-end-color"?: import("csstype").Property.BorderBlockEndColor | undefined;
            "border-block-end-style"?: import("csstype").Property.BorderBlockEndStyle | undefined;
            "border-block-end-width"?: import("csstype").Property.BorderBlockEndWidth<string | number> | undefined;
            "border-block-start-color"?: import("csstype").Property.BorderBlockStartColor | undefined;
            "border-block-start-style"?: import("csstype").Property.BorderBlockStartStyle | undefined;
            "border-block-start-width"?: import("csstype").Property.BorderBlockStartWidth<string | number> | undefined;
            "border-block-style"?: import("csstype").Property.BorderBlockStyle | undefined;
            "border-block-width"?: import("csstype").Property.BorderBlockWidth<string | number> | undefined;
            "border-bottom-color"?: import("csstype").Property.BorderBottomColor | undefined;
            "border-bottom-left-radius"?: import("csstype").Property.BorderBottomLeftRadius<string | number> | undefined;
            "border-bottom-right-radius"?: import("csstype").Property.BorderBottomRightRadius<string | number> | undefined;
            "border-bottom-style"?: import("csstype").Property.BorderBottomStyle | undefined;
            "border-bottom-width"?: import("csstype").Property.BorderBottomWidth<string | number> | undefined;
            "border-collapse"?: import("csstype").Property.BorderCollapse | undefined;
            "border-end-end-radius"?: import("csstype").Property.BorderEndEndRadius<string | number> | undefined;
            "border-end-start-radius"?: import("csstype").Property.BorderEndStartRadius<string | number> | undefined;
            "border-image-outset"?: import("csstype").Property.BorderImageOutset<string | number> | undefined;
            "border-image-repeat"?: import("csstype").Property.BorderImageRepeat | undefined;
            "border-image-slice"?: import("csstype").Property.BorderImageSlice | undefined;
            "border-image-source"?: import("csstype").Property.BorderImageSource | undefined;
            "border-image-width"?: import("csstype").Property.BorderImageWidth<string | number> | undefined;
            "border-inline-color"?: import("csstype").Property.BorderInlineColor | undefined;
            "border-inline-end-color"?: import("csstype").Property.BorderInlineEndColor | undefined;
            "border-inline-end-style"?: import("csstype").Property.BorderInlineEndStyle | undefined;
            "border-inline-end-width"?: import("csstype").Property.BorderInlineEndWidth<string | number> | undefined;
            "border-inline-start-color"?: import("csstype").Property.BorderInlineStartColor | undefined;
            "border-inline-start-style"?: import("csstype").Property.BorderInlineStartStyle | undefined;
            "border-inline-start-width"?: import("csstype").Property.BorderInlineStartWidth<string | number> | undefined;
            "border-inline-style"?: import("csstype").Property.BorderInlineStyle | undefined;
            "border-inline-width"?: import("csstype").Property.BorderInlineWidth<string | number> | undefined;
            "border-left-color"?: import("csstype").Property.BorderLeftColor | undefined;
            "border-left-style"?: import("csstype").Property.BorderLeftStyle | undefined;
            "border-left-width"?: import("csstype").Property.BorderLeftWidth<string | number> | undefined;
            "border-right-color"?: import("csstype").Property.BorderRightColor | undefined;
            "border-right-style"?: import("csstype").Property.BorderRightStyle | undefined;
            "border-right-width"?: import("csstype").Property.BorderRightWidth<string | number> | undefined;
            "border-spacing"?: import("csstype").Property.BorderSpacing<string | number> | undefined;
            "border-start-end-radius"?: import("csstype").Property.BorderStartEndRadius<string | number> | undefined;
            "border-start-start-radius"?: import("csstype").Property.BorderStartStartRadius<string | number> | undefined;
            "border-top-color"?: import("csstype").Property.BorderTopColor | undefined;
            "border-top-left-radius"?: import("csstype").Property.BorderTopLeftRadius<string | number> | undefined;
            "border-top-right-radius"?: import("csstype").Property.BorderTopRightRadius<string | number> | undefined;
            "border-top-style"?: import("csstype").Property.BorderTopStyle | undefined;
            "border-top-width"?: import("csstype").Property.BorderTopWidth<string | number> | undefined;
            "box-decoration-break"?: import("csstype").Property.BoxDecorationBreak | undefined;
            "box-shadow"?: import("csstype").Property.BoxShadow | undefined;
            "box-sizing"?: import("csstype").Property.BoxSizing | undefined;
            "break-after"?: import("csstype").Property.BreakAfter | undefined;
            "break-before"?: import("csstype").Property.BreakBefore | undefined;
            "break-inside"?: import("csstype").Property.BreakInside | undefined;
            "caption-side"?: import("csstype").Property.CaptionSide | undefined;
            "caret-color"?: import("csstype").Property.CaretColor | undefined;
            "caret-shape"?: import("csstype").Property.CaretShape | undefined;
            "clip-path"?: import("csstype").Property.ClipPath | undefined;
            "color-adjust"?: import("csstype").Property.PrintColorAdjust | undefined;
            "color-scheme"?: import("csstype").Property.ColorScheme | undefined;
            "column-count"?: import("csstype").Property.ColumnCount | undefined;
            "column-fill"?: import("csstype").Property.ColumnFill | undefined;
            "column-gap"?: import("csstype").Property.ColumnGap<string | number> | undefined;
            "column-rule-color"?: import("csstype").Property.ColumnRuleColor | undefined;
            "column-rule-style"?: import("csstype").Property.ColumnRuleStyle | undefined;
            "column-rule-width"?: import("csstype").Property.ColumnRuleWidth<string | number> | undefined;
            "column-span"?: import("csstype").Property.ColumnSpan | undefined;
            "column-width"?: import("csstype").Property.ColumnWidth<string | number> | undefined;
            "contain-intrinsic-block-size"?: import("csstype").Property.ContainIntrinsicBlockSize<string | number> | undefined;
            "contain-intrinsic-height"?: import("csstype").Property.ContainIntrinsicHeight<string | number> | undefined;
            "contain-intrinsic-inline-size"?: import("csstype").Property.ContainIntrinsicInlineSize<string | number> | undefined;
            "contain-intrinsic-width"?: import("csstype").Property.ContainIntrinsicWidth<string | number> | undefined;
            "container-name"?: import("csstype").Property.ContainerName | undefined;
            "container-type"?: import("csstype").Property.ContainerType | undefined;
            "content-visibility"?: import("csstype").Property.ContentVisibility | undefined;
            "counter-increment"?: import("csstype").Property.CounterIncrement | undefined;
            "counter-reset"?: import("csstype").Property.CounterReset | undefined;
            "counter-set"?: import("csstype").Property.CounterSet | undefined;
            "empty-cells"?: import("csstype").Property.EmptyCells | undefined;
            "flex-basis"?: import("csstype").Property.FlexBasis<string | number> | undefined;
            "flex-direction"?: import("csstype").Property.FlexDirection | undefined;
            "flex-grow"?: import("csstype").Property.FlexGrow | undefined;
            "flex-shrink"?: import("csstype").Property.FlexShrink | undefined;
            "flex-wrap"?: import("csstype").Property.FlexWrap | undefined;
            "font-family"?: import("csstype").Property.FontFamily | undefined;
            "font-feature-settings"?: import("csstype").Property.FontFeatureSettings | undefined;
            "font-kerning"?: import("csstype").Property.FontKerning | undefined;
            "font-language-override"?: import("csstype").Property.FontLanguageOverride | undefined;
            "font-optical-sizing"?: import("csstype").Property.FontOpticalSizing | undefined;
            "font-palette"?: import("csstype").Property.FontPalette | undefined;
            "font-size"?: import("csstype").Property.FontSize<string | number> | undefined;
            "font-size-adjust"?: import("csstype").Property.FontSizeAdjust | undefined;
            "font-smooth"?: import("csstype").Property.FontSmooth<string | number> | undefined;
            "font-stretch"?: import("csstype").Property.FontStretch | undefined;
            "font-style"?: import("csstype").Property.FontStyle | undefined;
            "font-synthesis"?: import("csstype").Property.FontSynthesis | undefined;
            "font-synthesis-position"?: import("csstype").Property.FontSynthesisPosition | undefined;
            "font-synthesis-small-caps"?: import("csstype").Property.FontSynthesisSmallCaps | undefined;
            "font-synthesis-style"?: import("csstype").Property.FontSynthesisStyle | undefined;
            "font-synthesis-weight"?: import("csstype").Property.FontSynthesisWeight | undefined;
            "font-variant"?: import("csstype").Property.FontVariant | undefined;
            "font-variant-alternates"?: import("csstype").Property.FontVariantAlternates | undefined;
            "font-variant-caps"?: import("csstype").Property.FontVariantCaps | undefined;
            "font-variant-east-asian"?: import("csstype").Property.FontVariantEastAsian | undefined;
            "font-variant-emoji"?: import("csstype").Property.FontVariantEmoji | undefined;
            "font-variant-ligatures"?: import("csstype").Property.FontVariantLigatures | undefined;
            "font-variant-numeric"?: import("csstype").Property.FontVariantNumeric | undefined;
            "font-variant-position"?: import("csstype").Property.FontVariantPosition | undefined;
            "font-variation-settings"?: import("csstype").Property.FontVariationSettings | undefined;
            "font-weight"?: import("csstype").Property.FontWeight | undefined;
            "forced-color-adjust"?: import("csstype").Property.ForcedColorAdjust | undefined;
            "grid-auto-columns"?: import("csstype").Property.GridAutoColumns<string | number> | undefined;
            "grid-auto-flow"?: import("csstype").Property.GridAutoFlow | undefined;
            "grid-auto-rows"?: import("csstype").Property.GridAutoRows<string | number> | undefined;
            "grid-column-end"?: import("csstype").Property.GridColumnEnd | undefined;
            "grid-column-start"?: import("csstype").Property.GridColumnStart | undefined;
            "grid-row-end"?: import("csstype").Property.GridRowEnd | undefined;
            "grid-row-start"?: import("csstype").Property.GridRowStart | undefined;
            "grid-template-areas"?: import("csstype").Property.GridTemplateAreas | undefined;
            "grid-template-columns"?: import("csstype").Property.GridTemplateColumns<string | number> | undefined;
            "grid-template-rows"?: import("csstype").Property.GridTemplateRows<string | number> | undefined;
            "hanging-punctuation"?: import("csstype").Property.HangingPunctuation | undefined;
            "hyphenate-character"?: import("csstype").Property.HyphenateCharacter | undefined;
            "hyphenate-limit-chars"?: import("csstype").Property.HyphenateLimitChars | undefined;
            "image-orientation"?: import("csstype").Property.ImageOrientation | undefined;
            "image-rendering"?: import("csstype").Property.ImageRendering | undefined;
            "image-resolution"?: import("csstype").Property.ImageResolution | undefined;
            "initial-letter"?: import("csstype").Property.InitialLetter | undefined;
            "inline-size"?: import("csstype").Property.InlineSize<string | number> | undefined;
            "input-security"?: import("csstype").Property.InputSecurity | undefined;
            "inset-block-end"?: import("csstype").Property.InsetBlockEnd<string | number> | undefined;
            "inset-block-start"?: import("csstype").Property.InsetBlockStart<string | number> | undefined;
            "inset-inline-end"?: import("csstype").Property.InsetInlineEnd<string | number> | undefined;
            "inset-inline-start"?: import("csstype").Property.InsetInlineStart<string | number> | undefined;
            "justify-content"?: import("csstype").Property.JustifyContent | undefined;
            "justify-items"?: import("csstype").Property.JustifyItems | undefined;
            "justify-self"?: import("csstype").Property.JustifySelf | undefined;
            "justify-tracks"?: import("csstype").Property.JustifyTracks | undefined;
            "letter-spacing"?: import("csstype").Property.LetterSpacing<string | number> | undefined;
            "line-break"?: import("csstype").Property.LineBreak | undefined;
            "line-height"?: import("csstype").Property.LineHeight<string | number> | undefined;
            "line-height-step"?: import("csstype").Property.LineHeightStep<string | number> | undefined;
            "list-style-image"?: import("csstype").Property.ListStyleImage | undefined;
            "list-style-position"?: import("csstype").Property.ListStylePosition | undefined;
            "list-style-type"?: import("csstype").Property.ListStyleType | undefined;
            "margin-block-end"?: import("csstype").Property.MarginBlockEnd<string | number> | undefined;
            "margin-block-start"?: import("csstype").Property.MarginBlockStart<string | number> | undefined;
            "margin-bottom"?: import("csstype").Property.MarginBottom<string | number> | undefined;
            "margin-inline-end"?: import("csstype").Property.MarginInlineEnd<string | number> | undefined;
            "margin-inline-start"?: import("csstype").Property.MarginInlineStart<string | number> | undefined;
            "margin-left"?: import("csstype").Property.MarginLeft<string | number> | undefined;
            "margin-right"?: import("csstype").Property.MarginRight<string | number> | undefined;
            "margin-top"?: import("csstype").Property.MarginTop<string | number> | undefined;
            "margin-trim"?: import("csstype").Property.MarginTrim | undefined;
            "mask-border-mode"?: import("csstype").Property.MaskBorderMode | undefined;
            "mask-border-outset"?: import("csstype").Property.MaskBorderOutset<string | number> | undefined;
            "mask-border-repeat"?: import("csstype").Property.MaskBorderRepeat | undefined;
            "mask-border-slice"?: import("csstype").Property.MaskBorderSlice | undefined;
            "mask-border-source"?: import("csstype").Property.MaskBorderSource | undefined;
            "mask-border-width"?: import("csstype").Property.MaskBorderWidth<string | number> | undefined;
            "mask-clip"?: import("csstype").Property.MaskClip | undefined;
            "mask-composite"?: import("csstype").Property.MaskComposite | undefined;
            "mask-image"?: import("csstype").Property.MaskImage | undefined;
            "mask-mode"?: import("csstype").Property.MaskMode | undefined;
            "mask-origin"?: import("csstype").Property.MaskOrigin | undefined;
            "mask-position"?: import("csstype").Property.MaskPosition<string | number> | undefined;
            "mask-repeat"?: import("csstype").Property.MaskRepeat | undefined;
            "mask-size"?: import("csstype").Property.MaskSize<string | number> | undefined;
            "mask-type"?: import("csstype").Property.MaskType | undefined;
            "masonry-auto-flow"?: import("csstype").Property.MasonryAutoFlow | undefined;
            "math-depth"?: import("csstype").Property.MathDepth | undefined;
            "math-shift"?: import("csstype").Property.MathShift | undefined;
            "math-style"?: import("csstype").Property.MathStyle | undefined;
            "max-block-size"?: import("csstype").Property.MaxBlockSize<string | number> | undefined;
            "max-height"?: import("csstype").Property.MaxHeight<string | number> | undefined;
            "max-inline-size"?: import("csstype").Property.MaxInlineSize<string | number> | undefined;
            "max-lines"?: import("csstype").Property.MaxLines | undefined;
            "max-width"?: import("csstype").Property.MaxWidth<string | number> | undefined;
            "min-block-size"?: import("csstype").Property.MinBlockSize<string | number> | undefined;
            "min-height"?: import("csstype").Property.MinHeight<string | number> | undefined;
            "min-inline-size"?: import("csstype").Property.MinInlineSize<string | number> | undefined;
            "min-width"?: import("csstype").Property.MinWidth<string | number> | undefined;
            "mix-blend-mode"?: import("csstype").Property.MixBlendMode | undefined;
            "motion-distance"?: import("csstype").Property.OffsetDistance<string | number> | undefined;
            "motion-path"?: import("csstype").Property.OffsetPath | undefined;
            "motion-rotation"?: import("csstype").Property.OffsetRotate | undefined;
            "object-fit"?: import("csstype").Property.ObjectFit | undefined;
            "object-position"?: import("csstype").Property.ObjectPosition<string | number> | undefined;
            "offset-anchor"?: import("csstype").Property.OffsetAnchor<string | number> | undefined;
            "offset-distance"?: import("csstype").Property.OffsetDistance<string | number> | undefined;
            "offset-path"?: import("csstype").Property.OffsetPath | undefined;
            "offset-position"?: import("csstype").Property.OffsetPosition<string | number> | undefined;
            "offset-rotate"?: import("csstype").Property.OffsetRotate | undefined;
            "offset-rotation"?: import("csstype").Property.OffsetRotate | undefined;
            "outline-color"?: import("csstype").Property.OutlineColor | undefined;
            "outline-offset"?: import("csstype").Property.OutlineOffset<string | number> | undefined;
            "outline-style"?: import("csstype").Property.OutlineStyle | undefined;
            "outline-width"?: import("csstype").Property.OutlineWidth<string | number> | undefined;
            "overflow-anchor"?: import("csstype").Property.OverflowAnchor | undefined;
            "overflow-block"?: import("csstype").Property.OverflowBlock | undefined;
            "overflow-clip-box"?: import("csstype").Property.OverflowClipBox | undefined;
            "overflow-clip-margin"?: import("csstype").Property.OverflowClipMargin<string | number> | undefined;
            "overflow-inline"?: import("csstype").Property.OverflowInline | undefined;
            "overflow-wrap"?: import("csstype").Property.OverflowWrap | undefined;
            "overflow-x"?: import("csstype").Property.OverflowX | undefined;
            "overflow-y"?: import("csstype").Property.OverflowY | undefined;
            "overscroll-behavior-block"?: import("csstype").Property.OverscrollBehaviorBlock | undefined;
            "overscroll-behavior-inline"?: import("csstype").Property.OverscrollBehaviorInline | undefined;
            "overscroll-behavior-x"?: import("csstype").Property.OverscrollBehaviorX | undefined;
            "overscroll-behavior-y"?: import("csstype").Property.OverscrollBehaviorY | undefined;
            "padding-block-end"?: import("csstype").Property.PaddingBlockEnd<string | number> | undefined;
            "padding-block-start"?: import("csstype").Property.PaddingBlockStart<string | number> | undefined;
            "padding-bottom"?: import("csstype").Property.PaddingBottom<string | number> | undefined;
            "padding-inline-end"?: import("csstype").Property.PaddingInlineEnd<string | number> | undefined;
            "padding-inline-start"?: import("csstype").Property.PaddingInlineStart<string | number> | undefined;
            "padding-left"?: import("csstype").Property.PaddingLeft<string | number> | undefined;
            "padding-right"?: import("csstype").Property.PaddingRight<string | number> | undefined;
            "padding-top"?: import("csstype").Property.PaddingTop<string | number> | undefined;
            "page-break-after"?: import("csstype").Property.PageBreakAfter | undefined;
            "page-break-before"?: import("csstype").Property.PageBreakBefore | undefined;
            "page-break-inside"?: import("csstype").Property.PageBreakInside | undefined;
            "paint-order"?: import("csstype").Property.PaintOrder | undefined;
            "perspective-origin"?: import("csstype").Property.PerspectiveOrigin<string | number> | undefined;
            "pointer-events"?: import("csstype").Property.PointerEvents | undefined;
            "print-color-adjust"?: import("csstype").Property.PrintColorAdjust | undefined;
            "row-gap"?: import("csstype").Property.RowGap<string | number> | undefined;
            "ruby-align"?: import("csstype").Property.RubyAlign | undefined;
            "ruby-merge"?: import("csstype").Property.RubyMerge | undefined;
            "ruby-position"?: import("csstype").Property.RubyPosition | undefined;
            "scroll-behavior"?: import("csstype").Property.ScrollBehavior | undefined;
            "scroll-margin-block-end"?: import("csstype").Property.ScrollMarginBlockEnd<string | number> | undefined;
            "scroll-margin-block-start"?: import("csstype").Property.ScrollMarginBlockStart<string | number> | undefined;
            "scroll-margin-bottom"?: import("csstype").Property.ScrollMarginBottom<string | number> | undefined;
            "scroll-margin-inline-end"?: import("csstype").Property.ScrollMarginInlineEnd<string | number> | undefined;
            "scroll-margin-inline-start"?: import("csstype").Property.ScrollMarginInlineStart<string | number> | undefined;
            "scroll-margin-left"?: import("csstype").Property.ScrollMarginLeft<string | number> | undefined;
            "scroll-margin-right"?: import("csstype").Property.ScrollMarginRight<string | number> | undefined;
            "scroll-margin-top"?: import("csstype").Property.ScrollMarginTop<string | number> | undefined;
            "scroll-padding-block-end"?: import("csstype").Property.ScrollPaddingBlockEnd<string | number> | undefined;
            "scroll-padding-block-start"?: import("csstype").Property.ScrollPaddingBlockStart<string | number> | undefined;
            "scroll-padding-bottom"?: import("csstype").Property.ScrollPaddingBottom<string | number> | undefined;
            "scroll-padding-inline-end"?: import("csstype").Property.ScrollPaddingInlineEnd<string | number> | undefined;
            "scroll-padding-inline-start"?: import("csstype").Property.ScrollPaddingInlineStart<string | number> | undefined;
            "scroll-padding-left"?: import("csstype").Property.ScrollPaddingLeft<string | number> | undefined;
            "scroll-padding-right"?: import("csstype").Property.ScrollPaddingRight<string | number> | undefined;
            "scroll-padding-top"?: import("csstype").Property.ScrollPaddingTop<string | number> | undefined;
            "scroll-snap-align"?: import("csstype").Property.ScrollSnapAlign | undefined;
            "scroll-snap-margin-bottom"?: import("csstype").Property.ScrollMarginBottom<string | number> | undefined;
            "scroll-snap-margin-left"?: import("csstype").Property.ScrollMarginLeft<string | number> | undefined;
            "scroll-snap-margin-right"?: import("csstype").Property.ScrollMarginRight<string | number> | undefined;
            "scroll-snap-margin-top"?: import("csstype").Property.ScrollMarginTop<string | number> | undefined;
            "scroll-snap-stop"?: import("csstype").Property.ScrollSnapStop | undefined;
            "scroll-snap-type"?: import("csstype").Property.ScrollSnapType | undefined;
            "scroll-timeline-axis"?: import("csstype").Property.ScrollTimelineAxis | undefined;
            "scroll-timeline-name"?: import("csstype").Property.ScrollTimelineName | undefined;
            "scrollbar-color"?: import("csstype").Property.ScrollbarColor | undefined;
            "scrollbar-gutter"?: import("csstype").Property.ScrollbarGutter | undefined;
            "scrollbar-width"?: import("csstype").Property.ScrollbarWidth | undefined;
            "shape-image-threshold"?: import("csstype").Property.ShapeImageThreshold | undefined;
            "shape-margin"?: import("csstype").Property.ShapeMargin<string | number> | undefined;
            "shape-outside"?: import("csstype").Property.ShapeOutside | undefined;
            "tab-size"?: import("csstype").Property.TabSize<string | number> | undefined;
            "table-layout"?: import("csstype").Property.TableLayout | undefined;
            "text-align"?: import("csstype").Property.TextAlign | undefined;
            "text-align-last"?: import("csstype").Property.TextAlignLast | undefined;
            "text-combine-upright"?: import("csstype").Property.TextCombineUpright | undefined;
            "text-decoration-color"?: import("csstype").Property.TextDecorationColor | undefined;
            "text-decoration-line"?: import("csstype").Property.TextDecorationLine | undefined;
            "text-decoration-skip"?: import("csstype").Property.TextDecorationSkip | undefined;
            "text-decoration-skip-ink"?: import("csstype").Property.TextDecorationSkipInk | undefined;
            "text-decoration-style"?: import("csstype").Property.TextDecorationStyle | undefined;
            "text-decoration-thickness"?: import("csstype").Property.TextDecorationThickness<string | number> | undefined;
            "text-emphasis-color"?: import("csstype").Property.TextEmphasisColor | undefined;
            "text-emphasis-position"?: import("csstype").Property.TextEmphasisPosition | undefined;
            "text-emphasis-style"?: import("csstype").Property.TextEmphasisStyle | undefined;
            "text-indent"?: import("csstype").Property.TextIndent<string | number> | undefined;
            "text-justify"?: import("csstype").Property.TextJustify | undefined;
            "text-orientation"?: import("csstype").Property.TextOrientation | undefined;
            "text-overflow"?: import("csstype").Property.TextOverflow | undefined;
            "text-rendering"?: import("csstype").Property.TextRendering | undefined;
            "text-shadow"?: import("csstype").Property.TextShadow | undefined;
            "text-size-adjust"?: import("csstype").Property.TextSizeAdjust | undefined;
            "text-transform"?: import("csstype").Property.TextTransform | undefined;
            "text-underline-offset"?: import("csstype").Property.TextUnderlineOffset<string | number> | undefined;
            "text-underline-position"?: import("csstype").Property.TextUnderlinePosition | undefined;
            "text-wrap"?: import("csstype").Property.TextWrap | undefined;
            "timeline-scope"?: import("csstype").Property.TimelineScope | undefined;
            "touch-action"?: import("csstype").Property.TouchAction | undefined;
            "transform-box"?: import("csstype").Property.TransformBox | undefined;
            "transform-origin"?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            "transform-style"?: import("csstype").Property.TransformStyle | undefined;
            "transition-behavior"?: import("csstype").Property.TransitionBehavior | undefined;
            "transition-delay"?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            "transition-duration"?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            "transition-property"?: import("csstype").Property.TransitionProperty | undefined;
            "transition-timing-function"?: import("csstype").Property.TransitionTimingFunction | undefined;
            "unicode-bidi"?: import("csstype").Property.UnicodeBidi | undefined;
            "user-select"?: import("csstype").Property.UserSelect | undefined;
            "vertical-align"?: import("csstype").Property.VerticalAlign<string | number> | undefined;
            "view-timeline-axis"?: import("csstype").Property.ViewTimelineAxis | undefined;
            "view-timeline-inset"?: import("csstype").Property.ViewTimelineInset<string | number> | undefined;
            "view-timeline-name"?: import("csstype").Property.ViewTimelineName | undefined;
            "view-transition-name"?: import("csstype").Property.ViewTransitionName | undefined;
            "white-space"?: import("csstype").Property.WhiteSpace | undefined;
            "white-space-collapse"?: import("csstype").Property.WhiteSpaceCollapse | undefined;
            "white-space-trim"?: import("csstype").Property.WhiteSpaceTrim | undefined;
            "will-change"?: import("csstype").Property.WillChange | undefined;
            "word-break"?: import("csstype").Property.WordBreak | undefined;
            "word-spacing"?: import("csstype").Property.WordSpacing<string | number> | undefined;
            "word-wrap"?: import("csstype").Property.WordWrap | undefined;
            "writing-mode"?: import("csstype").Property.WritingMode | undefined;
            "z-index"?: import("csstype").Property.ZIndex | undefined;
            "animation-range"?: import("csstype").Property.AnimationRange<string | number> | undefined;
            "background-position"?: import("csstype").Property.BackgroundPosition<string | number> | undefined;
            "border-block"?: import("csstype").Property.BorderBlock<string | number> | undefined;
            "border-block-end"?: import("csstype").Property.BorderBlockEnd<string | number> | undefined;
            "border-block-start"?: import("csstype").Property.BorderBlockStart<string | number> | undefined;
            "border-bottom"?: import("csstype").Property.BorderBottom<string | number> | undefined;
            "border-color"?: import("csstype").Property.BorderColor | undefined;
            "border-image"?: import("csstype").Property.BorderImage | undefined;
            "border-inline"?: import("csstype").Property.BorderInline<string | number> | undefined;
            "border-inline-end"?: import("csstype").Property.BorderInlineEnd<string | number> | undefined;
            "border-inline-start"?: import("csstype").Property.BorderInlineStart<string | number> | undefined;
            "border-left"?: import("csstype").Property.BorderLeft<string | number> | undefined;
            "border-radius"?: import("csstype").Property.BorderRadius<string | number> | undefined;
            "border-right"?: import("csstype").Property.BorderRight<string | number> | undefined;
            "border-style"?: import("csstype").Property.BorderStyle | undefined;
            "border-top"?: import("csstype").Property.BorderTop<string | number> | undefined;
            "border-width"?: import("csstype").Property.BorderWidth<string | number> | undefined;
            "column-rule"?: import("csstype").Property.ColumnRule<string | number> | undefined;
            "contain-intrinsic-size"?: import("csstype").Property.ContainIntrinsicSize<string | number> | undefined;
            "flex-flow"?: import("csstype").Property.FlexFlow | undefined;
            "grid-area"?: import("csstype").Property.GridArea | undefined;
            "grid-column"?: import("csstype").Property.GridColumn | undefined;
            "grid-row"?: import("csstype").Property.GridRow | undefined;
            "grid-template"?: import("csstype").Property.GridTemplate | undefined;
            "inset-block"?: import("csstype").Property.InsetBlock<string | number> | undefined;
            "inset-inline"?: import("csstype").Property.InsetInline<string | number> | undefined;
            "line-clamp"?: import("csstype").Property.LineClamp | undefined;
            "list-style"?: import("csstype").Property.ListStyle | undefined;
            "margin-block"?: import("csstype").Property.MarginBlock<string | number> | undefined;
            "margin-inline"?: import("csstype").Property.MarginInline<string | number> | undefined;
            "mask-border"?: import("csstype").Property.MaskBorder | undefined;
            "overscroll-behavior"?: import("csstype").Property.OverscrollBehavior | undefined;
            "padding-block"?: import("csstype").Property.PaddingBlock<string | number> | undefined;
            "padding-inline"?: import("csstype").Property.PaddingInline<string | number> | undefined;
            "place-content"?: import("csstype").Property.PlaceContent | undefined;
            "place-items"?: import("csstype").Property.PlaceItems | undefined;
            "place-self"?: import("csstype").Property.PlaceSelf | undefined;
            "scroll-margin"?: import("csstype").Property.ScrollMargin<string | number> | undefined;
            "scroll-margin-block"?: import("csstype").Property.ScrollMarginBlock<string | number> | undefined;
            "scroll-margin-inline"?: import("csstype").Property.ScrollMarginInline<string | number> | undefined;
            "scroll-padding"?: import("csstype").Property.ScrollPadding<string | number> | undefined;
            "scroll-padding-block"?: import("csstype").Property.ScrollPaddingBlock<string | number> | undefined;
            "scroll-padding-inline"?: import("csstype").Property.ScrollPaddingInline<string | number> | undefined;
            "scroll-snap-margin"?: import("csstype").Property.ScrollMargin<string | number> | undefined;
            "scroll-timeline"?: import("csstype").Property.ScrollTimeline | undefined;
            "text-decoration"?: import("csstype").Property.TextDecoration<string | number> | undefined;
            "text-emphasis"?: import("csstype").Property.TextEmphasis | undefined;
            "view-timeline"?: import("csstype").Property.ViewTimeline | undefined;
            "-moz-animation-delay"?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            "-moz-animation-direction"?: import("csstype").Property.AnimationDirection | undefined;
            "-moz-animation-duration"?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            "-moz-animation-fill-mode"?: import("csstype").Property.AnimationFillMode | undefined;
            "-moz-animation-iteration-count"?: import("csstype").Property.AnimationIterationCount | undefined;
            "-moz-animation-name"?: import("csstype").Property.AnimationName | undefined;
            "-moz-animation-play-state"?: import("csstype").Property.AnimationPlayState | undefined;
            "-moz-animation-timing-function"?: import("csstype").Property.AnimationTimingFunction | undefined;
            "-moz-appearance"?: import("csstype").Property.MozAppearance | undefined;
            "-moz-binding"?: import("csstype").Property.MozBinding | undefined;
            "-moz-border-bottom-colors"?: import("csstype").Property.MozBorderBottomColors | undefined;
            "-moz-border-end-color"?: import("csstype").Property.BorderInlineEndColor | undefined;
            "-moz-border-end-style"?: import("csstype").Property.BorderInlineEndStyle | undefined;
            "-moz-border-end-width"?: import("csstype").Property.BorderInlineEndWidth<string | number> | undefined;
            "-moz-border-left-colors"?: import("csstype").Property.MozBorderLeftColors | undefined;
            "-moz-border-right-colors"?: import("csstype").Property.MozBorderRightColors | undefined;
            "-moz-border-start-color"?: import("csstype").Property.BorderInlineStartColor | undefined;
            "-moz-border-start-style"?: import("csstype").Property.BorderInlineStartStyle | undefined;
            "-moz-border-top-colors"?: import("csstype").Property.MozBorderTopColors | undefined;
            "-moz-box-sizing"?: import("csstype").Property.BoxSizing | undefined;
            "-moz-column-count"?: import("csstype").Property.ColumnCount | undefined;
            "-moz-column-fill"?: import("csstype").Property.ColumnFill | undefined;
            "-moz-column-rule-color"?: import("csstype").Property.ColumnRuleColor | undefined;
            "-moz-column-rule-style"?: import("csstype").Property.ColumnRuleStyle | undefined;
            "-moz-column-rule-width"?: import("csstype").Property.ColumnRuleWidth<string | number> | undefined;
            "-moz-column-width"?: import("csstype").Property.ColumnWidth<string | number> | undefined;
            "-moz-context-properties"?: import("csstype").Property.MozContextProperties | undefined;
            "-moz-font-feature-settings"?: import("csstype").Property.FontFeatureSettings | undefined;
            "-moz-font-language-override"?: import("csstype").Property.FontLanguageOverride | undefined;
            "-moz-hyphens"?: import("csstype").Property.Hyphens | undefined;
            "-moz-image-region"?: import("csstype").Property.MozImageRegion | undefined;
            "-moz-margin-end"?: import("csstype").Property.MarginInlineEnd<string | number> | undefined;
            "-moz-margin-start"?: import("csstype").Property.MarginInlineStart<string | number> | undefined;
            "-moz-orient"?: import("csstype").Property.MozOrient | undefined;
            "-moz-osx-font-smoothing"?: import("csstype").Property.FontSmooth<string | number> | undefined;
            "-moz-outline-radius-bottomleft"?: import("csstype").Property.MozOutlineRadiusBottomleft<string | number> | undefined;
            "-moz-outline-radius-bottomright"?: import("csstype").Property.MozOutlineRadiusBottomright<string | number> | undefined;
            "-moz-outline-radius-topleft"?: import("csstype").Property.MozOutlineRadiusTopleft<string | number> | undefined;
            "-moz-outline-radius-topright"?: import("csstype").Property.MozOutlineRadiusTopright<string | number> | undefined;
            "-moz-padding-end"?: import("csstype").Property.PaddingInlineEnd<string | number> | undefined;
            "-moz-padding-start"?: import("csstype").Property.PaddingInlineStart<string | number> | undefined;
            "-moz-stack-sizing"?: import("csstype").Property.MozStackSizing | undefined;
            "-moz-tab-size"?: import("csstype").Property.TabSize<string | number> | undefined;
            "-moz-text-blink"?: import("csstype").Property.MozTextBlink | undefined;
            "-moz-text-size-adjust"?: import("csstype").Property.TextSizeAdjust | undefined;
            "-moz-user-focus"?: import("csstype").Property.MozUserFocus | undefined;
            "-moz-user-modify"?: import("csstype").Property.MozUserModify | undefined;
            "-moz-user-select"?: import("csstype").Property.UserSelect | undefined;
            "-moz-window-dragging"?: import("csstype").Property.MozWindowDragging | undefined;
            "-moz-window-shadow"?: import("csstype").Property.MozWindowShadow | undefined;
            "-ms-accelerator"?: import("csstype").Property.MsAccelerator | undefined;
            "-ms-block-progression"?: import("csstype").Property.MsBlockProgression | undefined;
            "-ms-content-zoom-chaining"?: import("csstype").Property.MsContentZoomChaining | undefined;
            "-ms-content-zoom-limit-max"?: import("csstype").Property.MsContentZoomLimitMax | undefined;
            "-ms-content-zoom-limit-min"?: import("csstype").Property.MsContentZoomLimitMin | undefined;
            "-ms-content-zoom-snap-points"?: import("csstype").Property.MsContentZoomSnapPoints | undefined;
            "-ms-content-zoom-snap-type"?: import("csstype").Property.MsContentZoomSnapType | undefined;
            "-ms-content-zooming"?: import("csstype").Property.MsContentZooming | undefined;
            "-ms-filter"?: import("csstype").Property.MsFilter | undefined;
            "-ms-flex-direction"?: import("csstype").Property.FlexDirection | undefined;
            "-ms-flex-positive"?: import("csstype").Property.FlexGrow | undefined;
            "-ms-flow-from"?: import("csstype").Property.MsFlowFrom | undefined;
            "-ms-flow-into"?: import("csstype").Property.MsFlowInto | undefined;
            "-ms-grid-columns"?: import("csstype").Property.MsGridColumns<string | number> | undefined;
            "-ms-grid-rows"?: import("csstype").Property.MsGridRows<string | number> | undefined;
            "-ms-high-contrast-adjust"?: import("csstype").Property.MsHighContrastAdjust | undefined;
            "-ms-hyphenate-limit-chars"?: import("csstype").Property.MsHyphenateLimitChars | undefined;
            "-ms-hyphenate-limit-lines"?: import("csstype").Property.MsHyphenateLimitLines | undefined;
            "-ms-hyphenate-limit-zone"?: import("csstype").Property.MsHyphenateLimitZone<string | number> | undefined;
            "-ms-hyphens"?: import("csstype").Property.Hyphens | undefined;
            "-ms-ime-align"?: import("csstype").Property.MsImeAlign | undefined;
            "-ms-line-break"?: import("csstype").Property.LineBreak | undefined;
            "-ms-order"?: import("csstype").Property.Order | undefined;
            "-ms-overflow-style"?: import("csstype").Property.MsOverflowStyle | undefined;
            "-ms-overflow-x"?: import("csstype").Property.OverflowX | undefined;
            "-ms-overflow-y"?: import("csstype").Property.OverflowY | undefined;
            "-ms-scroll-chaining"?: import("csstype").Property.MsScrollChaining | undefined;
            "-ms-scroll-limit-x-max"?: import("csstype").Property.MsScrollLimitXMax<string | number> | undefined;
            "-ms-scroll-limit-x-min"?: import("csstype").Property.MsScrollLimitXMin<string | number> | undefined;
            "-ms-scroll-limit-y-max"?: import("csstype").Property.MsScrollLimitYMax<string | number> | undefined;
            "-ms-scroll-limit-y-min"?: import("csstype").Property.MsScrollLimitYMin<string | number> | undefined;
            "-ms-scroll-rails"?: import("csstype").Property.MsScrollRails | undefined;
            "-ms-scroll-snap-points-x"?: import("csstype").Property.MsScrollSnapPointsX | undefined;
            "-ms-scroll-snap-points-y"?: import("csstype").Property.MsScrollSnapPointsY | undefined;
            "-ms-scroll-snap-type"?: import("csstype").Property.MsScrollSnapType | undefined;
            "-ms-scroll-translation"?: import("csstype").Property.MsScrollTranslation | undefined;
            "-ms-scrollbar-3dlight-color"?: import("csstype").Property.MsScrollbar3dlightColor | undefined;
            "-ms-scrollbar-arrow-color"?: import("csstype").Property.MsScrollbarArrowColor | undefined;
            "-ms-scrollbar-base-color"?: import("csstype").Property.MsScrollbarBaseColor | undefined;
            "-ms-scrollbar-darkshadow-color"?: import("csstype").Property.MsScrollbarDarkshadowColor | undefined;
            "-ms-scrollbar-face-color"?: import("csstype").Property.MsScrollbarFaceColor | undefined;
            "-ms-scrollbar-highlight-color"?: import("csstype").Property.MsScrollbarHighlightColor | undefined;
            "-ms-scrollbar-shadow-color"?: import("csstype").Property.MsScrollbarShadowColor | undefined;
            "-ms-scrollbar-track-color"?: import("csstype").Property.MsScrollbarTrackColor | undefined;
            "-ms-text-autospace"?: import("csstype").Property.MsTextAutospace | undefined;
            "-ms-text-combine-horizontal"?: import("csstype").Property.TextCombineUpright | undefined;
            "-ms-text-overflow"?: import("csstype").Property.TextOverflow | undefined;
            "-ms-touch-action"?: import("csstype").Property.TouchAction | undefined;
            "-ms-touch-select"?: import("csstype").Property.MsTouchSelect | undefined;
            "-ms-transform"?: import("csstype").Property.Transform | undefined;
            "-ms-transform-origin"?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            "-ms-transition-delay"?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            "-ms-transition-duration"?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            "-ms-transition-property"?: import("csstype").Property.TransitionProperty | undefined;
            "-ms-transition-timing-function"?: import("csstype").Property.TransitionTimingFunction | undefined;
            "-ms-user-select"?: import("csstype").Property.MsUserSelect | undefined;
            "-ms-word-break"?: import("csstype").Property.WordBreak | undefined;
            "-ms-wrap-flow"?: import("csstype").Property.MsWrapFlow | undefined;
            "-ms-wrap-margin"?: import("csstype").Property.MsWrapMargin<string | number> | undefined;
            "-ms-wrap-through"?: import("csstype").Property.MsWrapThrough | undefined;
            "-ms-writing-mode"?: import("csstype").Property.WritingMode | undefined;
            "-webkit-align-content"?: import("csstype").Property.AlignContent | undefined;
            "-webkit-align-items"?: import("csstype").Property.AlignItems | undefined;
            "-webkit-align-self"?: import("csstype").Property.AlignSelf | undefined;
            "-webkit-animation-delay"?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            "-webkit-animation-direction"?: import("csstype").Property.AnimationDirection | undefined;
            "-webkit-animation-duration"?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            "-webkit-animation-fill-mode"?: import("csstype").Property.AnimationFillMode | undefined;
            "-webkit-animation-iteration-count"?: import("csstype").Property.AnimationIterationCount | undefined;
            "-webkit-animation-name"?: import("csstype").Property.AnimationName | undefined;
            "-webkit-animation-play-state"?: import("csstype").Property.AnimationPlayState | undefined;
            "-webkit-animation-timing-function"?: import("csstype").Property.AnimationTimingFunction | undefined;
            "-webkit-appearance"?: import("csstype").Property.WebkitAppearance | undefined;
            "-webkit-backdrop-filter"?: import("csstype").Property.BackdropFilter | undefined;
            "-webkit-backface-visibility"?: import("csstype").Property.BackfaceVisibility | undefined;
            "-webkit-background-clip"?: import("csstype").Property.BackgroundClip | undefined;
            "-webkit-background-origin"?: import("csstype").Property.BackgroundOrigin | undefined;
            "-webkit-background-size"?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            "-webkit-border-before-color"?: import("csstype").Property.WebkitBorderBeforeColor | undefined;
            "-webkit-border-before-style"?: import("csstype").Property.WebkitBorderBeforeStyle | undefined;
            "-webkit-border-before-width"?: import("csstype").Property.WebkitBorderBeforeWidth<string | number> | undefined;
            "-webkit-border-bottom-left-radius"?: import("csstype").Property.BorderBottomLeftRadius<string | number> | undefined;
            "-webkit-border-bottom-right-radius"?: import("csstype").Property.BorderBottomRightRadius<string | number> | undefined;
            "-webkit-border-image-slice"?: import("csstype").Property.BorderImageSlice | undefined;
            "-webkit-border-top-left-radius"?: import("csstype").Property.BorderTopLeftRadius<string | number> | undefined;
            "-webkit-border-top-right-radius"?: import("csstype").Property.BorderTopRightRadius<string | number> | undefined;
            "-webkit-box-decoration-break"?: import("csstype").Property.BoxDecorationBreak | undefined;
            "-webkit-box-reflect"?: import("csstype").Property.WebkitBoxReflect<string | number> | undefined;
            "-webkit-box-shadow"?: import("csstype").Property.BoxShadow | undefined;
            "-webkit-box-sizing"?: import("csstype").Property.BoxSizing | undefined;
            "-webkit-clip-path"?: import("csstype").Property.ClipPath | undefined;
            "-webkit-column-count"?: import("csstype").Property.ColumnCount | undefined;
            "-webkit-column-fill"?: import("csstype").Property.ColumnFill | undefined;
            "-webkit-column-rule-color"?: import("csstype").Property.ColumnRuleColor | undefined;
            "-webkit-column-rule-style"?: import("csstype").Property.ColumnRuleStyle | undefined;
            "-webkit-column-rule-width"?: import("csstype").Property.ColumnRuleWidth<string | number> | undefined;
            "-webkit-column-span"?: import("csstype").Property.ColumnSpan | undefined;
            "-webkit-column-width"?: import("csstype").Property.ColumnWidth<string | number> | undefined;
            "-webkit-filter"?: import("csstype").Property.Filter | undefined;
            "-webkit-flex-basis"?: import("csstype").Property.FlexBasis<string | number> | undefined;
            "-webkit-flex-direction"?: import("csstype").Property.FlexDirection | undefined;
            "-webkit-flex-grow"?: import("csstype").Property.FlexGrow | undefined;
            "-webkit-flex-shrink"?: import("csstype").Property.FlexShrink | undefined;
            "-webkit-flex-wrap"?: import("csstype").Property.FlexWrap | undefined;
            "-webkit-font-feature-settings"?: import("csstype").Property.FontFeatureSettings | undefined;
            "-webkit-font-kerning"?: import("csstype").Property.FontKerning | undefined;
            "-webkit-font-smoothing"?: import("csstype").Property.FontSmooth<string | number> | undefined;
            "-webkit-font-variant-ligatures"?: import("csstype").Property.FontVariantLigatures | undefined;
            "-webkit-hyphenate-character"?: import("csstype").Property.HyphenateCharacter | undefined;
            "-webkit-hyphens"?: import("csstype").Property.Hyphens | undefined;
            "-webkit-initial-letter"?: import("csstype").Property.InitialLetter | undefined;
            "-webkit-justify-content"?: import("csstype").Property.JustifyContent | undefined;
            "-webkit-line-break"?: import("csstype").Property.LineBreak | undefined;
            "-webkit-line-clamp"?: import("csstype").Property.WebkitLineClamp | undefined;
            "-webkit-margin-end"?: import("csstype").Property.MarginInlineEnd<string | number> | undefined;
            "-webkit-margin-start"?: import("csstype").Property.MarginInlineStart<string | number> | undefined;
            "-webkit-mask-attachment"?: import("csstype").Property.WebkitMaskAttachment | undefined;
            "-webkit-mask-box-image-outset"?: import("csstype").Property.MaskBorderOutset<string | number> | undefined;
            "-webkit-mask-box-image-repeat"?: import("csstype").Property.MaskBorderRepeat | undefined;
            "-webkit-mask-box-image-slice"?: import("csstype").Property.MaskBorderSlice | undefined;
            "-webkit-mask-box-image-source"?: import("csstype").Property.MaskBorderSource | undefined;
            "-webkit-mask-box-image-width"?: import("csstype").Property.MaskBorderWidth<string | number> | undefined;
            "-webkit-mask-clip"?: import("csstype").Property.WebkitMaskClip | undefined;
            "-webkit-mask-composite"?: import("csstype").Property.WebkitMaskComposite | undefined;
            "-webkit-mask-image"?: import("csstype").Property.WebkitMaskImage | undefined;
            "-webkit-mask-origin"?: import("csstype").Property.WebkitMaskOrigin | undefined;
            "-webkit-mask-position"?: import("csstype").Property.WebkitMaskPosition<string | number> | undefined;
            "-webkit-mask-position-x"?: import("csstype").Property.WebkitMaskPositionX<string | number> | undefined;
            "-webkit-mask-position-y"?: import("csstype").Property.WebkitMaskPositionY<string | number> | undefined;
            "-webkit-mask-repeat"?: import("csstype").Property.WebkitMaskRepeat | undefined;
            "-webkit-mask-repeat-x"?: import("csstype").Property.WebkitMaskRepeatX | undefined;
            "-webkit-mask-repeat-y"?: import("csstype").Property.WebkitMaskRepeatY | undefined;
            "-webkit-mask-size"?: import("csstype").Property.WebkitMaskSize<string | number> | undefined;
            "-webkit-max-inline-size"?: import("csstype").Property.MaxInlineSize<string | number> | undefined;
            "-webkit-order"?: import("csstype").Property.Order | undefined;
            "-webkit-overflow-scrolling"?: import("csstype").Property.WebkitOverflowScrolling | undefined;
            "-webkit-padding-end"?: import("csstype").Property.PaddingInlineEnd<string | number> | undefined;
            "-webkit-padding-start"?: import("csstype").Property.PaddingInlineStart<string | number> | undefined;
            "-webkit-perspective"?: import("csstype").Property.Perspective<string | number> | undefined;
            "-webkit-perspective-origin"?: import("csstype").Property.PerspectiveOrigin<string | number> | undefined;
            "-webkit-print-color-adjust"?: import("csstype").Property.PrintColorAdjust | undefined;
            "-webkit-ruby-position"?: import("csstype").Property.RubyPosition | undefined;
            "-webkit-scroll-snap-type"?: import("csstype").Property.ScrollSnapType | undefined;
            "-webkit-shape-margin"?: import("csstype").Property.ShapeMargin<string | number> | undefined;
            "-webkit-tap-highlight-color"?: import("csstype").Property.WebkitTapHighlightColor | undefined;
            "-webkit-text-combine"?: import("csstype").Property.TextCombineUpright | undefined;
            "-webkit-text-decoration-color"?: import("csstype").Property.TextDecorationColor | undefined;
            "-webkit-text-decoration-line"?: import("csstype").Property.TextDecorationLine | undefined;
            "-webkit-text-decoration-skip"?: import("csstype").Property.TextDecorationSkip | undefined;
            "-webkit-text-decoration-style"?: import("csstype").Property.TextDecorationStyle | undefined;
            "-webkit-text-emphasis-color"?: import("csstype").Property.TextEmphasisColor | undefined;
            "-webkit-text-emphasis-position"?: import("csstype").Property.TextEmphasisPosition | undefined;
            "-webkit-text-emphasis-style"?: import("csstype").Property.TextEmphasisStyle | undefined;
            "-webkit-text-fill-color"?: import("csstype").Property.WebkitTextFillColor | undefined;
            "-webkit-text-orientation"?: import("csstype").Property.TextOrientation | undefined;
            "-webkit-text-size-adjust"?: import("csstype").Property.TextSizeAdjust | undefined;
            "-webkit-text-stroke-color"?: import("csstype").Property.WebkitTextStrokeColor | undefined;
            "-webkit-text-stroke-width"?: import("csstype").Property.WebkitTextStrokeWidth<string | number> | undefined;
            "-webkit-text-underline-position"?: import("csstype").Property.TextUnderlinePosition | undefined;
            "-webkit-touch-callout"?: import("csstype").Property.WebkitTouchCallout | undefined;
            "-webkit-transform"?: import("csstype").Property.Transform | undefined;
            "-webkit-transform-origin"?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            "-webkit-transform-style"?: import("csstype").Property.TransformStyle | undefined;
            "-webkit-transition-delay"?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            "-webkit-transition-duration"?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            "-webkit-transition-property"?: import("csstype").Property.TransitionProperty | undefined;
            "-webkit-transition-timing-function"?: import("csstype").Property.TransitionTimingFunction | undefined;
            "-webkit-user-modify"?: import("csstype").Property.WebkitUserModify | undefined;
            "-webkit-user-select"?: import("csstype").Property.UserSelect | undefined;
            "-webkit-writing-mode"?: import("csstype").Property.WritingMode | undefined;
            "-moz-animation"?: import("csstype").Property.Animation<string & {}> | undefined;
            "-moz-border-image"?: import("csstype").Property.BorderImage | undefined;
            "-moz-column-rule"?: import("csstype").Property.ColumnRule<string | number> | undefined;
            "-moz-columns"?: import("csstype").Property.Columns<string | number> | undefined;
            "-moz-outline-radius"?: import("csstype").Property.MozOutlineRadius<string | number> | undefined;
            "-ms-content-zoom-limit"?: import("csstype").Property.MsContentZoomLimit | undefined;
            "-ms-content-zoom-snap"?: import("csstype").Property.MsContentZoomSnap | undefined;
            "-ms-flex"?: import("csstype").Property.Flex<string | number> | undefined;
            "-ms-scroll-limit"?: import("csstype").Property.MsScrollLimit | undefined;
            "-ms-scroll-snap-x"?: import("csstype").Property.MsScrollSnapX | undefined;
            "-ms-scroll-snap-y"?: import("csstype").Property.MsScrollSnapY | undefined;
            "-ms-transition"?: import("csstype").Property.Transition<string & {}> | undefined;
            "-webkit-animation"?: import("csstype").Property.Animation<string & {}> | undefined;
            "-webkit-border-before"?: import("csstype").Property.WebkitBorderBefore<string | number> | undefined;
            "-webkit-border-image"?: import("csstype").Property.BorderImage | undefined;
            "-webkit-border-radius"?: import("csstype").Property.BorderRadius<string | number> | undefined;
            "-webkit-column-rule"?: import("csstype").Property.ColumnRule<string | number> | undefined;
            "-webkit-columns"?: import("csstype").Property.Columns<string | number> | undefined;
            "-webkit-flex"?: import("csstype").Property.Flex<string | number> | undefined;
            "-webkit-flex-flow"?: import("csstype").Property.FlexFlow | undefined;
            "-webkit-mask"?: import("csstype").Property.WebkitMask<string | number> | undefined;
            "-webkit-mask-box-image"?: import("csstype").Property.MaskBorder | undefined;
            "-webkit-text-emphasis"?: import("csstype").Property.TextEmphasis | undefined;
            "-webkit-text-stroke"?: import("csstype").Property.WebkitTextStroke<string | number> | undefined;
            "-webkit-transition"?: import("csstype").Property.Transition<string & {}> | undefined;
            "box-align"?: import("csstype").Property.BoxAlign | undefined;
            "box-direction"?: import("csstype").Property.BoxDirection | undefined;
            "box-flex"?: import("csstype").Property.BoxFlex | undefined;
            "box-flex-group"?: import("csstype").Property.BoxFlexGroup | undefined;
            "box-lines"?: import("csstype").Property.BoxLines | undefined;
            "box-ordinal-group"?: import("csstype").Property.BoxOrdinalGroup | undefined;
            "box-orient"?: import("csstype").Property.BoxOrient | undefined;
            "box-pack"?: import("csstype").Property.BoxPack | undefined;
            "grid-column-gap"?: import("csstype").Property.GridColumnGap<string | number> | undefined;
            "grid-gap"?: import("csstype").Property.GridGap<string | number> | undefined;
            "grid-row-gap"?: import("csstype").Property.GridRowGap<string | number> | undefined;
            "ime-mode"?: import("csstype").Property.ImeMode | undefined;
            "offset-block"?: import("csstype").Property.InsetBlock<string | number> | undefined;
            "offset-block-end"?: import("csstype").Property.InsetBlockEnd<string | number> | undefined;
            "offset-block-start"?: import("csstype").Property.InsetBlockStart<string | number> | undefined;
            "offset-inline"?: import("csstype").Property.InsetInline<string | number> | undefined;
            "offset-inline-end"?: import("csstype").Property.InsetInlineEnd<string | number> | undefined;
            "offset-inline-start"?: import("csstype").Property.InsetInlineStart<string | number> | undefined;
            "scroll-snap-coordinate"?: import("csstype").Property.ScrollSnapCoordinate<string | number> | undefined;
            "scroll-snap-destination"?: import("csstype").Property.ScrollSnapDestination<string | number> | undefined;
            "scroll-snap-points-x"?: import("csstype").Property.ScrollSnapPointsX | undefined;
            "scroll-snap-points-y"?: import("csstype").Property.ScrollSnapPointsY | undefined;
            "scroll-snap-type-x"?: import("csstype").Property.ScrollSnapTypeX | undefined;
            "scroll-snap-type-y"?: import("csstype").Property.ScrollSnapTypeY | undefined;
            "-khtml-box-align"?: import("csstype").Property.BoxAlign | undefined;
            "-khtml-box-direction"?: import("csstype").Property.BoxDirection | undefined;
            "-khtml-box-flex"?: import("csstype").Property.BoxFlex | undefined;
            "-khtml-box-flex-group"?: import("csstype").Property.BoxFlexGroup | undefined;
            "-khtml-box-lines"?: import("csstype").Property.BoxLines | undefined;
            "-khtml-box-ordinal-group"?: import("csstype").Property.BoxOrdinalGroup | undefined;
            "-khtml-box-orient"?: import("csstype").Property.BoxOrient | undefined;
            "-khtml-box-pack"?: import("csstype").Property.BoxPack | undefined;
            "-khtml-line-break"?: import("csstype").Property.LineBreak | undefined;
            "-khtml-opacity"?: import("csstype").Property.Opacity | undefined;
            "-khtml-user-select"?: import("csstype").Property.UserSelect | undefined;
            "-moz-backface-visibility"?: import("csstype").Property.BackfaceVisibility | undefined;
            "-moz-background-clip"?: import("csstype").Property.BackgroundClip | undefined;
            "-moz-background-inline-policy"?: import("csstype").Property.BoxDecorationBreak | undefined;
            "-moz-background-origin"?: import("csstype").Property.BackgroundOrigin | undefined;
            "-moz-background-size"?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            "-moz-border-radius"?: import("csstype").Property.BorderRadius<string | number> | undefined;
            "-moz-border-radius-bottomleft"?: import("csstype").Property.BorderBottomLeftRadius<string | number> | undefined;
            "-moz-border-radius-bottomright"?: import("csstype").Property.BorderBottomRightRadius<string | number> | undefined;
            "-moz-border-radius-topleft"?: import("csstype").Property.BorderTopLeftRadius<string | number> | undefined;
            "-moz-border-radius-topright"?: import("csstype").Property.BorderTopRightRadius<string | number> | undefined;
            "-moz-box-align"?: import("csstype").Property.BoxAlign | undefined;
            "-moz-box-direction"?: import("csstype").Property.BoxDirection | undefined;
            "-moz-box-flex"?: import("csstype").Property.BoxFlex | undefined;
            "-moz-box-ordinal-group"?: import("csstype").Property.BoxOrdinalGroup | undefined;
            "-moz-box-orient"?: import("csstype").Property.BoxOrient | undefined;
            "-moz-box-pack"?: import("csstype").Property.BoxPack | undefined;
            "-moz-box-shadow"?: import("csstype").Property.BoxShadow | undefined;
            "-moz-float-edge"?: import("csstype").Property.MozFloatEdge | undefined;
            "-moz-force-broken-image-icon"?: import("csstype").Property.MozForceBrokenImageIcon | undefined;
            "-moz-opacity"?: import("csstype").Property.Opacity | undefined;
            "-moz-outline"?: import("csstype").Property.Outline<string | number> | undefined;
            "-moz-outline-color"?: import("csstype").Property.OutlineColor | undefined;
            "-moz-outline-style"?: import("csstype").Property.OutlineStyle | undefined;
            "-moz-outline-width"?: import("csstype").Property.OutlineWidth<string | number> | undefined;
            "-moz-perspective"?: import("csstype").Property.Perspective<string | number> | undefined;
            "-moz-perspective-origin"?: import("csstype").Property.PerspectiveOrigin<string | number> | undefined;
            "-moz-text-align-last"?: import("csstype").Property.TextAlignLast | undefined;
            "-moz-text-decoration-color"?: import("csstype").Property.TextDecorationColor | undefined;
            "-moz-text-decoration-line"?: import("csstype").Property.TextDecorationLine | undefined;
            "-moz-text-decoration-style"?: import("csstype").Property.TextDecorationStyle | undefined;
            "-moz-transform"?: import("csstype").Property.Transform | undefined;
            "-moz-transform-origin"?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            "-moz-transform-style"?: import("csstype").Property.TransformStyle | undefined;
            "-moz-transition"?: import("csstype").Property.Transition<string & {}> | undefined;
            "-moz-transition-delay"?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            "-moz-transition-duration"?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            "-moz-transition-property"?: import("csstype").Property.TransitionProperty | undefined;
            "-moz-transition-timing-function"?: import("csstype").Property.TransitionTimingFunction | undefined;
            "-moz-user-input"?: import("csstype").Property.MozUserInput | undefined;
            "-ms-ime-mode"?: import("csstype").Property.ImeMode | undefined;
            "-o-animation"?: import("csstype").Property.Animation<string & {}> | undefined;
            "-o-animation-delay"?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            "-o-animation-direction"?: import("csstype").Property.AnimationDirection | undefined;
            "-o-animation-duration"?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            "-o-animation-fill-mode"?: import("csstype").Property.AnimationFillMode | undefined;
            "-o-animation-iteration-count"?: import("csstype").Property.AnimationIterationCount | undefined;
            "-o-animation-name"?: import("csstype").Property.AnimationName | undefined;
            "-o-animation-play-state"?: import("csstype").Property.AnimationPlayState | undefined;
            "-o-animation-timing-function"?: import("csstype").Property.AnimationTimingFunction | undefined;
            "-o-background-size"?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            "-o-border-image"?: import("csstype").Property.BorderImage | undefined;
            "-o-object-fit"?: import("csstype").Property.ObjectFit | undefined;
            "-o-object-position"?: import("csstype").Property.ObjectPosition<string | number> | undefined;
            "-o-tab-size"?: import("csstype").Property.TabSize<string | number> | undefined;
            "-o-text-overflow"?: import("csstype").Property.TextOverflow | undefined;
            "-o-transform"?: import("csstype").Property.Transform | undefined;
            "-o-transform-origin"?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            "-o-transition"?: import("csstype").Property.Transition<string & {}> | undefined;
            "-o-transition-delay"?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            "-o-transition-duration"?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            "-o-transition-property"?: import("csstype").Property.TransitionProperty | undefined;
            "-o-transition-timing-function"?: import("csstype").Property.TransitionTimingFunction | undefined;
            "-webkit-box-align"?: import("csstype").Property.BoxAlign | undefined;
            "-webkit-box-direction"?: import("csstype").Property.BoxDirection | undefined;
            "-webkit-box-flex"?: import("csstype").Property.BoxFlex | undefined;
            "-webkit-box-flex-group"?: import("csstype").Property.BoxFlexGroup | undefined;
            "-webkit-box-lines"?: import("csstype").Property.BoxLines | undefined;
            "-webkit-box-ordinal-group"?: import("csstype").Property.BoxOrdinalGroup | undefined;
            "-webkit-box-orient"?: import("csstype").Property.BoxOrient | undefined;
            "-webkit-box-pack"?: import("csstype").Property.BoxPack | undefined;
            "alignment-baseline"?: import("csstype").Property.AlignmentBaseline | undefined;
            "baseline-shift"?: import("csstype").Property.BaselineShift<string | number> | undefined;
            "clip-rule"?: import("csstype").Property.ClipRule | undefined;
            "color-interpolation"?: import("csstype").Property.ColorInterpolation | undefined;
            "color-rendering"?: import("csstype").Property.ColorRendering | undefined;
            "dominant-baseline"?: import("csstype").Property.DominantBaseline | undefined;
            "fill-opacity"?: import("csstype").Property.FillOpacity | undefined;
            "fill-rule"?: import("csstype").Property.FillRule | undefined;
            "flood-color"?: import("csstype").Property.FloodColor | undefined;
            "flood-opacity"?: import("csstype").Property.FloodOpacity | undefined;
            "glyph-orientation-vertical"?: import("csstype").Property.GlyphOrientationVertical | undefined;
            "lighting-color"?: import("csstype").Property.LightingColor | undefined;
            "marker-end"?: import("csstype").Property.MarkerEnd | undefined;
            "marker-mid"?: import("csstype").Property.MarkerMid | undefined;
            "marker-start"?: import("csstype").Property.MarkerStart | undefined;
            "shape-rendering"?: import("csstype").Property.ShapeRendering | undefined;
            "stop-color"?: import("csstype").Property.StopColor | undefined;
            "stop-opacity"?: import("csstype").Property.StopOpacity | undefined;
            "stroke-dasharray"?: import("csstype").Property.StrokeDasharray<string | number> | undefined;
            "stroke-dashoffset"?: import("csstype").Property.StrokeDashoffset<string | number> | undefined;
            "stroke-linecap"?: import("csstype").Property.StrokeLinecap | undefined;
            "stroke-linejoin"?: import("csstype").Property.StrokeLinejoin | undefined;
            "stroke-miterlimit"?: import("csstype").Property.StrokeMiterlimit | undefined;
            "stroke-opacity"?: import("csstype").Property.StrokeOpacity | undefined;
            "stroke-width"?: import("csstype").Property.StrokeWidth<string | number> | undefined;
            "text-anchor"?: import("csstype").Property.TextAnchor | undefined;
            "vector-effect"?: import("csstype").Property.VectorEffect | undefined;
        } | undefined;
        hoverStyle?: CSSPropertiess;
        imgVariant?: "rectangle" | "square" | undefined;
        imgPreview?: boolean | undefined;
        imgPreviewMask?: boolean | undefined;
        status?: "uploading" | "done" | "error" | undefined;
        percent?: numbe;
        errorTip?: string | undefined;
        id?: number | undefined;
    }[], SelfFilesCardProps[] | {
        uid?: string | number | undefined;
        name?: string | undefined;
        fileSize?: number | undefined;
        fileType?: FilesType | undefined;
        description?: string | undefined;
        url?: string | undefined;
        thumbUrl?: string | undefined;
        imgFile?: {
            readonly lastModified: number;
            readonly name: string;
            readonly webkitRelativePath: string;
            readonly size: number;
            readonly type: string;
            arrayBuffer: () => Promise<ArrayBuffer>;
            bytes: () => Promise<Uint8Array>;
            slice: (start?: number, end?: number, contentType?: string) => Blob;
            stream: () => ReadableStream<Uint8Array>;
            text: () => Promise<string>;
        } | {
            readonly size: number;
            readonly type: string;
            arrayBuffer: () => Promise<ArrayBuffer>;
            bytes: () => Promise<Uint8Array>;
            slice: (start?: number, end?: number, contentType?: string) => Blob;
            stream: () => ReadableStream<Uint8Array>;
            text: () => Promise<string>;
        } | undefined;
        iconSize?: string | undefined;
        iconColor?: string | undefined;
        showDelIcon?: boolean | undefined;
        maxWidth?: string | undefined;
        noStyle?: boolean | undefined;
        style?: {
            [x: `--${string}`]: string | number | undefined;
            accentColor?: import("csstype").Property.AccentColor | undefined;
            alignContent?: import("csstype").Property.AlignContent | undefined;
            alignItems?: import("csstype").Property.AlignItems | undefined;
            alignSelf?: import("csstype").Property.AlignSelf | undefined;
            alignTracks?: import("csstype").Property.AlignTracks | undefined;
            animationComposition?: import("csstype").Property.AnimationComposition | undefined;
            animationDelay?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            animationDirection?: import("csstype").Property.AnimationDirection | undefined;
            animationDuration?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            animationFillMode?: import("csstype").Property.AnimationFillMode | undefined;
            animationIterationCount?: import("csstype").Property.AnimationIterationCount | undefined;
            animationName?: import("csstype").Property.AnimationName | undefined;
            animationPlayState?: import("csstype").Property.AnimationPlayState | undefined;
            animationRangeEnd?: import("csstype").Property.AnimationRangeEnd<string | number> | undefined;
            animationRangeStart?: import("csstype").Property.AnimationRangeStart<string | number> | undefined;
            animationTimeline?: import("csstype").Property.AnimationTimeline | undefined;
            animationTimingFunction?: import("csstype").Property.AnimationTimingFunction | undefined;
            appearance?: import("csstype").Property.Appearance | undefined;
            aspectRatio?: import("csstype").Property.AspectRatio | undefined;
            backdropFilter?: import("csstype").Property.BackdropFilter | undefined;
            backfaceVisibility?: import("csstype").Property.BackfaceVisibility | undefined;
            backgroundAttachment?: import("csstype").Property.BackgroundAttachment | undefined;
            backgroundBlendMode?: import("csstype").Property.BackgroundBlendMode | undefined;
            backgroundClip?: import("csstype").Property.BackgroundClip | undefined;
            backgroundColor?: import("csstype").Property.BackgroundColor | undefined;
            backgroundImage?: import("csstype").Property.BackgroundImage | undefined;
            backgroundOrigin?: import("csstype").Property.BackgroundOrigin | undefined;
            backgroundPositionX?: import("csstype").Property.BackgroundPositionX<string | number> | undefined;
            backgroundPositionY?: import("csstype").Property.BackgroundPositionY<string | number> | undefined;
            backgroundRepeat?: import("csstype").Property.BackgroundRepeat | undefined;
            backgroundSize?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            blockOverflow?: import("csstype").Property.BlockOverflow | undefined;
            blockSize?: import("csstype").Property.BlockSize<string | number> | undefined;
            borderBlockColor?: import("csstype").Property.BorderBlockColor | undefined;
            borderBlockEndColor?: import("csstype").Property.BorderBlockEndColor | undefined;
            borderBlockEndStyle?: import("csstype").Property.BorderBlockEndStyle | undefined;
            borderBlockEndWidth?: import("csstype").Property.BorderBlockEndWidth<string | number> | undefined;
            borderBlockStartColor?: import("csstype").Property.BorderBlockStartColor | undefined;
            borderBlockStartStyle?: import("csstype").Property.BorderBlockStartStyle | undefined;
            borderBlockStartWidth?: import("csstype").Property.BorderBlockStartWidth<string | number> | undefined;
            borderBlockStyle?: import("csstype").Property.BorderBlockStyle | undefined;
            borderBlockWidth?: import("csstype").Property.BorderBlockWidth<string | number> | undefined;
            borderBottomColor?: import("csstype").Property.BorderBottomColor | undefined;
            borderBottomLeftRadius?: import("csstype").Property.BorderBottomLeftRadius<string | number> | undefined;
            borderBottomRightRadius?: import("csstype").Property.BorderBottomRightRadius<string | number> | undefined;
            borderBottomStyle?: import("csstype").Property.BorderBottomStyle | undefined;
            borderBottomWidth?: import("csstype").Property.BorderBottomWidth<string | number> | undefined;
            borderCollapse?: import("csstype").Property.BorderCollapse | undefined;
            borderEndEndRadius?: import("csstype").Property.BorderEndEndRadius<string | number> | undefined;
            borderEndStartRadius?: import("csstype").Property.BorderEndStartRadius<string | number> | undefined;
            borderImageOutset?: import("csstype").Property.BorderImageOutset<string | number> | undefined;
            borderImageRepeat?: import("csstype").Property.BorderImageRepeat | undefined;
            borderImageSlice?: import("csstype").Property.BorderImageSlice | undefined;
            borderImageSource?: import("csstype").Property.BorderImageSource | undefined;
            borderImageWidth?: import("csstype").Property.BorderImageWidth<string | number> | undefined;
            borderInlineColor?: import("csstype").Property.BorderInlineColor | undefined;
            borderInlineEndColor?: import("csstype").Property.BorderInlineEndColor | undefined;
            borderInlineEndStyle?: import("csstype").Property.BorderInlineEndStyle | undefined;
            borderInlineEndWidth?: import("csstype").Property.BorderInlineEndWidth<string | number> | undefined;
            borderInlineStartColor?: import("csstype").Property.BorderInlineStartColor | undefined;
            borderInlineStartStyle?: import("csstype").Property.BorderInlineStartStyle | undefined;
            borderInlineStartWidth?: import("csstype").Property.BorderInlineStartWidth<string | number> | undefined;
            borderInlineStyle?: import("csstype").Property.BorderInlineStyle | undefined;
            borderInlineWidth?: import("csstype").Property.BorderInlineWidth<string | number> | undefined;
            borderLeftColor?: import("csstype").Property.BorderLeftColor | undefined;
            borderLeftStyle?: import("csstype").Property.BorderLeftStyle | undefined;
            borderLeftWidth?: import("csstype").Property.BorderLeftWidth<string | number> | undefined;
            borderRightColor?: import("csstype").Property.BorderRightColor | undefined;
            borderRightStyle?: import("csstype").Property.BorderRightStyle | undefined;
            borderRightWidth?: import("csstype").Property.BorderRightWidth<string | number> | undefined;
            borderSpacing?: import("csstype").Property.BorderSpacing<string | number> | undefined;
            borderStartEndRadius?: import("csstype").Property.BorderStartEndRadius<string | number> | undefined;
            borderStartStartRadius?: import("csstype").Property.BorderStartStartRadius<string | number> | undefined;
            borderTopColor?: import("csstype").Property.BorderTopColor | undefined;
            borderTopLeftRadius?: import("csstype").Property.BorderTopLeftRadius<string | number> | undefined;
            borderTopRightRadius?: import("csstype").Property.BorderTopRightRadius<string | number> | undefined;
            borderTopStyle?: import("csstype").Property.BorderTopStyle | undefined;
            borderTopWidth?: import("csstype").Property.BorderTopWidth<string | number> | undefined;
            bottom?: import("csstype").Property.Bottom<string | number> | undefined;
            boxDecorationBreak?: import("csstype").Property.BoxDecorationBreak | undefined;
            boxShadow?: import("csstype").Property.BoxShadow | undefined;
            boxSizing?: import("csstype").Property.BoxSizing | undefined;
            breakAfter?: import("csstype").Property.BreakAfter | undefined;
            breakBefore?: import("csstype").Property.BreakBefore | undefined;
            breakInside?: import("csstype").Property.BreakInside | undefined;
            captionSide?: import("csstype").Property.CaptionSide | undefined;
            caretColor?: import("csstype").Property.CaretColor | undefined;
            caretShape?: import("csstype").Property.CaretShape | undefined;
            clear?: import("csstype").Property.Clear | undefined;
            clipPath?: import("csstype").Property.ClipPath | undefined;
            color?: import("csstype").Property.Color | undefined;
            colorAdjust?: import("csstype").Property.PrintColorAdjust | undefined;
            colorScheme?: import("csstype").Property.ColorScheme | undefined;
            columnCount?: import("csstype").Property.ColumnCount | undefined;
            columnFill?: import("csstype").Property.ColumnFill | undefined;
            columnGap?: import("csstype").Property.ColumnGap<string | number> | undefined;
            columnRuleColor?: import("csstype").Property.ColumnRuleColor | undefined;
            columnRuleStyle?: import("csstype").Property.ColumnRuleStyle | undefined;
            columnRuleWidth?: import("csstype").Property.ColumnRuleWidth<string | number> | undefined;
            columnSpan?: import("csstype").Property.ColumnSpan | undefined;
            columnWidth?: import("csstype").Property.ColumnWidth<string | number> | undefined;
            contain?: import("csstype").Property.Contain | undefined;
            containIntrinsicBlockSize?: import("csstype").Property.ContainIntrinsicBlockSize<string | number> | undefined;
            containIntrinsicHeight?: import("csstype").Property.ContainIntrinsicHeight<string | number> | undefined;
            containIntrinsicInlineSize?: import("csstype").Property.ContainIntrinsicInlineSize<string | number> | undefined;
            containIntrinsicWidth?: import("csstype").Property.ContainIntrinsicWidth<string | number> | undefined;
            containerName?: import("csstype").Property.ContainerName | undefined;
            containerType?: import("csstype").Property.ContainerType | undefined;
            content?: import("csstype").Property.Content | undefined;
            contentVisibility?: import("csstype").Property.ContentVisibility | undefined;
            counterIncrement?: import("csstype").Property.CounterIncrement | undefined;
            counterReset?: import("csstype").Property.CounterReset | undefined;
            counterSet?: import("csstype").Property.CounterSet | undefined;
            cursor?: import("csstype").Property.Cursor | undefined;
            direction?: import("csstype").Property.Direction | undefined;
            display?: import("csstype").Property.Display | undefined;
            emptyCells?: import("csstype").Property.EmptyCells | undefined;
            filter?: import("csstype").Property.Filter | undefined;
            flexBasis?: import("csstype").Property.FlexBasis<string | number> | undefined;
            flexDirection?: import("csstype").Property.FlexDirection | undefined;
            flexGrow?: import("csstype").Property.FlexGrow | undefined;
            flexShrink?: import("csstype").Property.FlexShrink | undefined;
            flexWrap?: import("csstype").Property.FlexWrap | undefined;
            float?: import("csstype").Property.Float | undefined;
            fontFamily?: import("csstype").Property.FontFamily | undefined;
            fontFeatureSettings?: import("csstype").Property.FontFeatureSettings | undefined;
            fontKerning?: import("csstype").Property.FontKerning | undefined;
            fontLanguageOverride?: import("csstype").Property.FontLanguageOverride | undefined;
            fontOpticalSizing?: import("csstype").Property.FontOpticalSizing | undefined;
            fontPalette?: import("csstype").Property.FontPalette | undefined;
            fontSize?: import("csstype").Property.FontSize<string | number> | undefined;
            fontSizeAdjust?: import("csstype").Property.FontSizeAdjust | undefined;
            fontSmooth?: import("csstype").Property.FontSmooth<string | number> | undefined;
            fontStretch?: import("csstype").Property.FontStretch | undefined;
            fontStyle?: import("csstype").Property.FontStyle | undefined;
            fontSynthesis?: import("csstype").Property.FontSynthesis | undefined;
            fontSynthesisPosition?: import("csstype").Property.FontSynthesisPosition | undefined;
            fontSynthesisSmallCaps?: import("csstype").Property.FontSynthesisSmallCaps | undefined;
            fontSynthesisStyle?: import("csstype").Property.FontSynthesisStyle | undefined;
            fontSynthesisWeight?: import("csstype").Property.FontSynthesisWeight | undefined;
            fontVariant?: import("csstype").Property.FontVariant | undefined;
            fontVariantAlternates?: import("csstype").Property.FontVariantAlternates | undefined;
            fontVariantCaps?: import("csstype").Property.FontVariantCaps | undefined;
            fontVariantEastAsian?: import("csstype").Property.FontVariantEastAsian | undefined;
            fontVariantEmoji?: import("csstype").Property.FontVariantEmoji | undefined;
            fontVariantLigatures?: import("csstype").Property.FontVariantLigatures | undefined;
            fontVariantNumeric?: import("csstype").Property.FontVariantNumeric | undefined;
            fontVariantPosition?: import("csstype").Property.FontVariantPosition | undefined;
            fontVariationSettings?: import("csstype").Property.FontVariationSettings | undefined;
            fontWeight?: import("csstype").Property.FontWeight | undefined;
            forcedColorAdjust?: import("csstype").Property.ForcedColorAdjust | undefined;
            gridAutoColumns?: import("csstype").Property.GridAutoColumns<string | number> | undefined;
            gridAutoFlow?: import("csstype").Property.GridAutoFlow | undefined;
            gridAutoRows?: import("csstype").Property.GridAutoRows<string | number> | undefined;
            gridColumnEnd?: import("csstype").Property.GridColumnEnd | undefined;
            gridColumnStart?: import("csstype").Property.GridColumnStart | undefined;
            gridRowEnd?: import("csstype").Property.GridRowEnd | undefined;
            gridRowStart?: import("csstype").Property.GridRowStart | undefined;
            gridTemplateAreas?: import("csstype").Property.GridTemplateAreas | undefined;
            gridTemplateColumns?: import("csstype").Property.GridTemplateColumns<string | number> | undefined;
            gridTemplateRows?: import("csstype").Property.GridTemplateRows<string | number> | undefined;
            hangingPunctuation?: import("csstype").Property.HangingPunctuation | undefined;
            height?: import("csstype").Property.Height<string | number> | undefined;
            hyphenateCharacter?: import("csstype").Property.HyphenateCharacter | undefined;
            hyphenateLimitChars?: import("csstype").Property.HyphenateLimitChars | undefined;
            hyphens?: import("csstype").Property.Hyphens | undefined;
            imageOrientation?: import("csstype").Property.ImageOrientation | undefined;
            imageRendering?: import("csstype").Property.ImageRendering | undefined;
            imageResolution?: import("csstype").Property.ImageResolution | undefined;
            initialLetter?: import("csstype").Property.InitialLetter | undefined;
            inlineSize?: import("csstype").Property.InlineSize<string | number> | undefined;
            inputSecurity?: import("csstype").Property.InputSecurity | undefined;
            insetBlockEnd?: import("csstype").Property.InsetBlockEnd<string | number> | undefined;
            insetBlockStart?: import("csstype").Property.InsetBlockStart<string | number> | undefined;
            insetInlineEnd?: import("csstype").Property.InsetInlineEnd<string | number> | undefined;
            insetInlineStart?: import("csstype").Property.InsetInlineStart<string | number> | undefined;
            isolation?: import("csstype").Property.Isolation | undefined;
            justifyContent?: import("csstype").Property.JustifyContent | undefined;
            justifyItems?: import("csstype").Property.JustifyItems | undefined;
            justifySelf?: import("csstype").Property.JustifySelf | undefined;
            justifyTracks?: import("csstype").Property.JustifyTracks | undefined;
            left?: import("csstype").Property.Left<string | number> | undefined;
            letterSpacing?: import("csstype").Property.LetterSpacing<string | number> | undefined;
            lineBreak?: import("csstype").Property.LineBreak | undefined;
            lineHeight?: import("csstype").Property.LineHeight<string | number> | undefined;
            lineHeightStep?: import("csstype").Property.LineHeightStep<string | number> | undefined;
            listStyleImage?: import("csstype").Property.ListStyleImage | undefined;
            listStylePosition?: import("csstype").Property.ListStylePosition | undefined;
            listStyleType?: import("csstype").Property.ListStyleType | undefined;
            marginBlockEnd?: import("csstype").Property.MarginBlockEnd<string | number> | undefined;
            marginBlockStart?: import("csstype").Property.MarginBlockStart<string | number> | undefined;
            marginBottom?: import("csstype").Property.MarginBottom<string | number> | undefined;
            marginInlineEnd?: import("csstype").Property.MarginInlineEnd<string | number> | undefined;
            marginInlineStart?: import("csstype").Property.MarginInlineStart<string | number> | undefined;
            marginLeft?: import("csstype").Property.MarginLeft<string | number> | undefined;
            marginRight?: import("csstype").Property.MarginRight<string | number> | undefined;
            marginTop?: import("csstype").Property.MarginTop<string | number> | undefined;
            marginTrim?: import("csstype").Property.MarginTrim | undefined;
            maskBorderMode?: import("csstype").Property.MaskBorderMode | undefined;
            maskBorderOutset?: import("csstype").Property.MaskBorderOutset<string | number> | undefined;
            maskBorderRepeat?: import("csstype").Property.MaskBorderRepeat | undefined;
            maskBorderSlice?: import("csstype").Property.MaskBorderSlice | undefined;
            maskBorderSource?: import("csstype").Property.MaskBorderSource | undefined;
            maskBorderWidth?: import("csstype").Property.MaskBorderWidth<string | number> | undefined;
            maskClip?: import("csstype").Property.MaskClip | undefined;
            maskComposite?: import("csstype").Property.MaskComposite | undefined;
            maskImage?: import("csstype").Property.MaskImage | undefined;
            maskMode?: import("csstype").Property.MaskMode | undefined;
            maskOrigin?: import("csstype").Property.MaskOrigin | undefined;
            maskPosition?: import("csstype").Property.MaskPosition<string | number> | undefined;
            maskRepeat?: import("csstype").Property.MaskRepeat | undefined;
            maskSize?: import("csstype").Property.MaskSize<string | number> | undefined;
            maskType?: import("csstype").Property.MaskType | undefined;
            masonryAutoFlow?: import("csstype").Property.MasonryAutoFlow | undefined;
            mathDepth?: import("csstype").Property.MathDepth | undefined;
            mathShift?: import("csstype").Property.MathShift | undefined;
            mathStyle?: import("csstype").Property.MathStyle | undefined;
            maxBlockSize?: import("csstype").Property.MaxBlockSize<string | number> | undefined;
            maxHeight?: import("csstype").Property.MaxHeight<string | number> | undefined;
            maxInlineSize?: import("csstype").Property.MaxInlineSize<string | number> | undefined;
            maxLines?: import("csstype").Property.MaxLines | undefined;
            maxWidth?: import("csstype").Property.MaxWidth<string | number> | undefined;
            minBlockSize?: import("csstype").Property.MinBlockSize<string | number> | undefined;
            minHeight?: import("csstype").Property.MinHeight<string | number> | undefined;
            minInlineSize?: import("csstype").Property.MinInlineSize<string | number> | undefined;
            minWidth?: import("csstype").Property.MinWidth<string | number> | undefined;
            mixBlendMode?: import("csstype").Property.MixBlendMode | undefined;
            motionDistance?: import("csstype").Property.OffsetDistance<string | number> | undefined;
            motionPath?: import("csstype").Property.OffsetPath | undefined;
            motionRotation?: import("csstype").Property.OffsetRotate | undefined;
            objectFit?: import("csstype").Property.ObjectFit | undefined;
            objectPosition?: import("csstype").Property.ObjectPosition<string | number> | undefined;
            offsetAnchor?: import("csstype").Property.OffsetAnchor<string | number> | undefined;
            offsetDistance?: import("csstype").Property.OffsetDistance<string | number> | undefined;
            offsetPath?: import("csstype").Property.OffsetPath | undefined;
            offsetPosition?: import("csstype").Property.OffsetPosition<string | number> | undefined;
            offsetRotate?: import("csstype").Property.OffsetRotate | undefined;
            offsetRotation?: import("csstype").Property.OffsetRotate | undefined;
            opacity?: import("csstype").Property.Opacity | undefined;
            order?: import("csstype").Property.Order | undefined;
            orphans?: import("csstype").Property.Orphans | undefined;
            outlineColor?: import("csstype").Property.OutlineColor | undefined;
            outlineOffset?: import("csstype").Property.OutlineOffset<string | number> | undefined;
            outlineStyle?: import("csstype").Property.OutlineStyle | undefined;
            outlineWidth?: import("csstype").Property.OutlineWidth<string | number> | undefined;
            overflowAnchor?: import("csstype").Property.OverflowAnchor | undefined;
            overflowBlock?: import("csstype").Property.OverflowBlock | undefined;
            overflowClipBox?: import("csstype").Property.OverflowClipBox | undefined;
            overflowClipMargin?: import("csstype").Property.OverflowClipMargin<string | number> | undefined;
            overflowInline?: import("csstype").Property.OverflowInline | undefined;
            overflowWrap?: import("csstype").Property.OverflowWrap | undefined;
            overflowX?: import("csstype").Property.OverflowX | undefined;
            overflowY?: import("csstype").Property.OverflowY | undefined;
            overlay?: import("csstype").Property.Overlay | undefined;
            overscrollBehaviorBlock?: import("csstype").Property.OverscrollBehaviorBlock | undefined;
            overscrollBehaviorInline?: import("csstype").Property.OverscrollBehaviorInline | undefined;
            overscrollBehaviorX?: import("csstype").Property.OverscrollBehaviorX | undefined;
            overscrollBehaviorY?: import("csstype").Property.OverscrollBehaviorY | undefined;
            paddingBlockEnd?: import("csstype").Property.PaddingBlockEnd<string | number> | undefined;
            paddingBlockStart?: import("csstype").Property.PaddingBlockStart<string | number> | undefined;
            paddingBottom?: import("csstype").Property.PaddingBottom<string | number> | undefined;
            paddingInlineEnd?: import("csstype").Property.PaddingInlineEnd<string | number> | undefined;
            paddingInlineStart?: import("csstype").Property.PaddingInlineStart<string | number> | undefined;
            paddingLeft?: import("csstype").Property.PaddingLeft<string | number> | undefined;
            paddingRight?: import("csstype").Property.PaddingRight<string | number> | undefined;
            paddingTop?: import("csstype").Property.PaddingTop<string | number> | undefined;
            page?: import("csstype").Property.Page | undefined;
            pageBreakAfter?: import("csstype").Property.PageBreakAfter | undefined;
            pageBreakBefore?: import("csstype").Property.PageBreakBefore | undefined;
            pageBreakInside?: import("csstype").Property.PageBreakInside | undefined;
            paintOrder?: import("csstype").Property.PaintOrder | undefined;
            perspective?: import("csstype").Property.Perspective<string | number> | undefined;
            perspectiveOrigin?: import("csstype").Property.PerspectiveOrigin<string | number> | undefined;
            pointerEvents?: import("csstype").Property.PointerEvents | undefined;
            position?: import("csstype").Property.Position | undefined;
            printColorAdjust?: import("csstype").Property.PrintColorAdjust | undefined;
            quotes?: import("csstype").Property.Quotes | undefined;
            resize?: import("csstype").Property.Resize | undefined;
            right?: import("csstype").Property.Right<string | number> | undefined;
            rotate?: import("csstype").Property.Rotate | undefined;
            rowGap?: import("csstype").Property.RowGap<string | number> | undefined;
            rubyAlign?: import("csstype").Property.RubyAlign | undefined;
            rubyMerge?: import("csstype").Property.RubyMerge | undefined;
            rubyPosition?: import("csstype").Property.RubyPosition | undefined;
            scale?: import("csstype").Property.Scale | undefined;
            scrollBehavior?: import("csstype").Property.ScrollBehavior | undefined;
            scrollMarginBlockEnd?: import("csstype").Property.ScrollMarginBlockEnd<string | number> | undefined;
            scrollMarginBlockStart?: import("csstype").Property.ScrollMarginBlockStart<string | number> | undefined;
            scrollMarginBottom?: import("csstype").Property.ScrollMarginBottom<string | number> | undefined;
            scrollMarginInlineEnd?: import("csstype").Property.ScrollMarginInlineEnd<string | number> | undefined;
            scrollMarginInlineStart?: import("csstype").Property.ScrollMarginInlineStart<string | number> | undefined;
            scrollMarginLeft?: import("csstype").Property.ScrollMarginLeft<string | number> | undefined;
            scrollMarginRight?: import("csstype").Property.ScrollMarginRight<string | number> | undefined;
            scrollMarginTop?: import("csstype").Property.ScrollMarginTop<string | number> | undefined;
            scrollPaddingBlockEnd?: import("csstype").Property.ScrollPaddingBlockEnd<string | number> | undefined;
            scrollPaddingBlockStart?: import("csstype").Property.ScrollPaddingBlockStart<string | number> | undefined;
            scrollPaddingBottom?: import("csstype").Property.ScrollPaddingBottom<string | number> | undefined;
            scrollPaddingInlineEnd?: import("csstype").Property.ScrollPaddingInlineEnd<string | number> | undefined;
            scrollPaddingInlineStart?: import("csstype").Property.ScrollPaddingInlineStart<string | number> | undefined;
            scrollPaddingLeft?: import("csstype").Property.ScrollPaddingLeft<string | number> | undefined;
            scrollPaddingRight?: import("csstype").Property.ScrollPaddingRight<string | number> | undefined;
            scrollPaddingTop?: import("csstype").Property.ScrollPaddingTop<string | number> | undefined;
            scrollSnapAlign?: import("csstype").Property.ScrollSnapAlign | undefined;
            scrollSnapMarginBottom?: import("csstype").Property.ScrollMarginBottom<string | number> | undefined;
            scrollSnapMarginLeft?: import("csstype").Property.ScrollMarginLeft<string | number> | undefined;
            scrollSnapMarginRight?: import("csstype").Property.ScrollMarginRight<string | number> | undefined;
            scrollSnapMarginTop?: import("csstype").Property.ScrollMarginTop<string | number> | undefined;
            scrollSnapStop?: import("csstype").Property.ScrollSnapStop | undefined;
            scrollSnapType?: import("csstype").Property.ScrollSnapType | undefined;
            scrollTimelineAxis?: import("csstype").Property.ScrollTimelineAxis | undefined;
            scrollTimelineName?: import("csstype").Property.ScrollTimelineName | undefined;
            scrollbarColor?: import("csstype").Property.ScrollbarColor | undefined;
            scrollbarGutter?: import("csstype").Property.ScrollbarGutter | undefined;
            scrollbarWidth?: import("csstype").Property.ScrollbarWidth | undefined;
            shapeImageThreshold?: import("csstype").Property.ShapeImageThreshold | undefined;
            shapeMargin?: import("csstype").Property.ShapeMargin<string | number> | undefined;
            shapeOutside?: import("csstype").Property.ShapeOutside | undefined;
            tabSize?: import("csstype").Property.TabSize<string | number> | undefined;
            tableLayout?: import("csstype").Property.TableLayout | undefined;
            textAlign?: import("csstype").Property.TextAlign | undefined;
            textAlignLast?: import("csstype").Property.TextAlignLast | undefined;
            textCombineUpright?: import("csstype").Property.TextCombineUpright | undefined;
            textDecorationColor?: import("csstype").Property.TextDecorationColor | undefined;
            textDecorationLine?: import("csstype").Property.TextDecorationLine | undefined;
            textDecorationSkip?: import("csstype").Property.TextDecorationSkip | undefined;
            textDecorationSkipInk?: import("csstype").Property.TextDecorationSkipInk | undefined;
            textDecorationStyle?: import("csstype").Property.TextDecorationStyle | undefined;
            textDecorationThickness?: import("csstype").Property.TextDecorationThickness<string | number> | undefined;
            textEmphasisColor?: import("csstype").Property.TextEmphasisColor | undefined;
            textEmphasisPosition?: import("csstype").Property.TextEmphasisPosition | undefined;
            textEmphasisStyle?: import("csstype").Property.TextEmphasisStyle | undefined;
            textIndent?: import("csstype").Property.TextIndent<string | number> | undefined;
            textJustify?: import("csstype").Property.TextJustify | undefined;
            textOrientation?: import("csstype").Property.TextOrientation | undefined;
            textOverflow?: import("csstype").Property.TextOverflow | undefined;
            textRendering?: import("csstype").Property.TextRendering | undefined;
            textShadow?: import("csstype").Property.TextShadow | undefined;
            textSizeAdjust?: import("csstype").Property.TextSizeAdjust | undefined;
            textTransform?: import("csstype").Property.TextTransform | undefined;
            textUnderlineOffset?: import("csstype").Property.TextUnderlineOffset<string | number> | undefined;
            textUnderlinePosition?: import("csstype").Property.TextUnderlinePosition | undefined;
            textWrap?: import("csstype").Property.TextWrap | undefined;
            timelineScope?: import("csstype").Property.TimelineScope | undefined;
            top?: import("csstype").Property.Top<string | number> | undefined;
            touchAction?: import("csstype").Property.TouchAction | undefined;
            transform?: import("csstype").Property.Transform | undefined;
            transformBox?: import("csstype").Property.TransformBox | undefined;
            transformOrigin?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            transformStyle?: import("csstype").Property.TransformStyle | undefined;
            transitionBehavior?: import("csstype").Property.TransitionBehavior | undefined;
            transitionDelay?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            transitionDuration?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            transitionProperty?: import("csstype").Property.TransitionProperty | undefined;
            transitionTimingFunction?: import("csstype").Property.TransitionTimingFunction | undefined;
            translate?: import("csstype").Property.Translate<string | number> | undefined;
            unicodeBidi?: import("csstype").Property.UnicodeBidi | undefined;
            userSelect?: import("csstype").Property.UserSelect | undefined;
            verticalAlign?: import("csstype").Property.VerticalAlign<string | number> | undefined;
            viewTimelineAxis?: import("csstype").Property.ViewTimelineAxis | undefined;
            viewTimelineInset?: import("csstype").Property.ViewTimelineInset<string | number> | undefined;
            viewTimelineName?: import("csstype").Property.ViewTimelineName | undefined;
            viewTransitionName?: import("csstype").Property.ViewTransitionName | undefined;
            visibility?: import("csstype").Property.Visibility | undefined;
            whiteSpace?: import("csstype").Property.WhiteSpace | undefined;
            whiteSpaceCollapse?: import("csstype").Property.WhiteSpaceCollapse | undefined;
            whiteSpaceTrim?: import("csstype").Property.WhiteSpaceTrim | undefined;
            widows?: import("csstype").Property.Widows | undefined;
            width?: import("csstype").Property.Width<string | number> | undefined;
            willChange?: import("csstype").Property.WillChange | undefined;
            wordBreak?: import("csstype").Property.WordBreak | undefined;
            wordSpacing?: import("csstype").Property.WordSpacing<string | number> | undefined;
            wordWrap?: import("csstype").Property.WordWrap | undefined;
            writingMode?: import("csstype").Property.WritingMode | undefined;
            zIndex?: import("csstype").Property.ZIndex | undefined;
            zoom?: import("csstype").Property.Zoom | undefined;
            all?: import('csstype').Globals | undefined;
            animation?: import("csstype").Property.Animation<string & {}> | undefined;
            animationRange?: import("csstype").Property.AnimationRange<string | number> | undefined;
            background?: import("csstype").Property.Background<string | number> | undefined;
            backgroundPosition?: import("csstype").Property.BackgroundPosition<string | number> | undefined;
            border?: import("csstype").Property.Border<string | number> | undefined;
            borderBlock?: import("csstype").Property.BorderBlock<string | number> | undefined;
            borderBlockEnd?: import("csstype").Property.BorderBlockEnd<string | number> | undefined;
            borderBlockStart?: import("csstype").Property.BorderBlockStart<string | number> | undefined;
            borderBottom?: import("csstype").Property.BorderBottom<string | number> | undefined;
            borderColor?: import("csstype").Property.BorderColor | undefined;
            borderImage?: import("csstype").Property.BorderImage | undefined;
            borderInline?: import("csstype").Property.BorderInline<string | number> | undefined;
            borderInlineEnd?: import("csstype").Property.BorderInlineEnd<string | number> | undefined;
            borderInlineStart?: import("csstype").Property.BorderInlineStart<string | number> | undefined;
            borderLeft?: import("csstype").Property.BorderLeft<string | number> | undefined;
            borderRadius?: import("csstype").Property.BorderRadius<string | number> | undefined;
            borderRight?: import("csstype").Property.BorderRight<string | number> | undefined;
            borderStyle?: import("csstype").Property.BorderStyle | undefined;
            borderTop?: import("csstype").Property.BorderTop<string | number> | undefined;
            borderWidth?: import("csstype").Property.BorderWidth<string | number> | undefined;
            caret?: import("csstype").Property.Caret | undefined;
            columnRule?: import("csstype").Property.ColumnRule<string | number> | undefined;
            columns?: import("csstype").Property.Columns<string | number> | undefined;
            containIntrinsicSize?: import("csstype").Property.ContainIntrinsicSize<string | number> | undefined;
            container?: import("csstype").Property.Container | undefined;
            flex?: import("csstype").Property.Flex<string | number> | undefined;
            flexFlow?: import("csstype").Property.FlexFlow | undefined;
            font?: import("csstype").Property.Font | undefined;
            gap?: import("csstype").Property.Gap<string | number> | undefined;
            grid?: import("csstype").Property.Grid | undefined;
            gridArea?: import("csstype").Property.GridArea | undefined;
            gridColumn?: import("csstype").Property.GridColumn | undefined;
            gridRow?: import("csstype").Property.GridRow | undefined;
            gridTemplate?: import("csstype").Property.GridTemplate | undefined;
            inset?: import("csstype").Property.Inset<string | number> | undefined;
            insetBlock?: import("csstype").Property.InsetBlock<string | number> | undefined;
            insetInline?: import("csstype").Property.InsetInline<string | number> | undefined;
            lineClamp?: import("csstype").Property.LineClamp | undefined;
            listStyle?: import("csstype").Property.ListStyle | undefined;
            margin?: import("csstype").Property.Margin<string | number> | undefined;
            marginBlock?: import("csstype").Property.MarginBlock<string | number> | undefined;
            marginInline?: import("csstype").Property.MarginInline<string | number> | undefined;
            mask?: import("csstype").Property.Mask<string | number> | undefined;
            maskBorder?: import("csstype").Property.MaskBorder | undefined;
            motion?: import("csstype").Property.Offset<string | number> | undefined;
            offset?: import("csstype").Property.Offset<string | number> | undefined;
            outline?: import("csstype").Property.Outline<string | number> | undefined;
            overflow?: import("csstype").Property.Overflow | undefined;
            overscrollBehavior?: import("csstype").Property.OverscrollBehavior | undefined;
            padding?: import("csstype").Property.Padding<string | number> | undefined;
            paddingBlock?: import("csstype").Property.PaddingBlock<string | number> | undefined;
            paddingInline?: import("csstype").Property.PaddingInline<string | number> | undefined;
            placeContent?: import("csstype").Property.PlaceContent | undefined;
            placeItems?: import("csstype").Property.PlaceItems | undefined;
            placeSelf?: import("csstype").Property.PlaceSelf | undefined;
            scrollMargin?: import("csstype").Property.ScrollMargin<string | number> | undefined;
            scrollMarginBlock?: import("csstype").Property.ScrollMarginBlock<string | number> | undefined;
            scrollMarginInline?: import("csstype").Property.ScrollMarginInline<string | number> | undefined;
            scrollPadding?: import("csstype").Property.ScrollPadding<string | number> | undefined;
            scrollPaddingBlock?: import("csstype").Property.ScrollPaddingBlock<string | number> | undefined;
            scrollPaddingInline?: import("csstype").Property.ScrollPaddingInline<string | number> | undefined;
            scrollSnapMargin?: import("csstype").Property.ScrollMargin<string | number> | undefined;
            scrollTimeline?: import("csstype").Property.ScrollTimeline | undefined;
            textDecoration?: import("csstype").Property.TextDecoration<string | number> | undefined;
            textEmphasis?: import("csstype").Property.TextEmphasis | undefined;
            transition?: import("csstype").Property.Transition<string & {}> | undefined;
            viewTimeline?: import("csstype").Property.ViewTimeline | undefined;
            MozAnimationDelay?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            MozAnimationDirection?: import("csstype").Property.AnimationDirection | undefined;
            MozAnimationDuration?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            MozAnimationFillMode?: import("csstype").Property.AnimationFillMode | undefined;
            MozAnimationIterationCount?: import("csstype").Property.AnimationIterationCount | undefined;
            MozAnimationName?: import("csstype").Property.AnimationName | undefined;
            MozAnimationPlayState?: import("csstype").Property.AnimationPlayState | undefined;
            MozAnimationTimingFunction?: import("csstype").Property.AnimationTimingFunction | undefined;
            MozAppearance?: import("csstype").Property.MozAppearance | undefined;
            MozBinding?: import("csstype").Property.MozBinding | undefined;
            MozBorderBottomColors?: import("csstype").Property.MozBorderBottomColors | undefined;
            MozBorderEndColor?: import("csstype").Property.BorderInlineEndColor | undefined;
            MozBorderEndStyle?: import("csstype").Property.BorderInlineEndStyle | undefined;
            MozBorderEndWidth?: import("csstype").Property.BorderInlineEndWidth<string | number> | undefined;
            MozBorderLeftColors?: import("csstype").Property.MozBorderLeftColors | undefined;
            MozBorderRightColors?: import("csstype").Property.MozBorderRightColors | undefined;
            MozBorderStartColor?: import("csstype").Property.BorderInlineStartColor | undefined;
            MozBorderStartStyle?: import("csstype").Property.BorderInlineStartStyle | undefined;
            MozBorderTopColors?: import("csstype").Property.MozBorderTopColors | undefined;
            MozBoxSizing?: import("csstype").Property.BoxSizing | undefined;
            MozColumnCount?: import("csstype").Property.ColumnCount | undefined;
            MozColumnFill?: import("csstype").Property.ColumnFill | undefined;
            MozColumnRuleColor?: import("csstype").Property.ColumnRuleColor | undefined;
            MozColumnRuleStyle?: import("csstype").Property.ColumnRuleStyle | undefined;
            MozColumnRuleWidth?: import("csstype").Property.ColumnRuleWidth<string | number> | undefined;
            MozColumnWidth?: import("csstype").Property.ColumnWidth<string | number> | undefined;
            MozContextProperties?: import("csstype").Property.MozContextProperties | undefined;
            MozFontFeatureSettings?: import("csstype").Property.FontFeatureSettings | undefined;
            MozFontLanguageOverride?: import("csstype").Property.FontLanguageOverride | undefined;
            MozHyphens?: import("csstype").Property.Hyphens | undefined;
            MozImageRegion?: import("csstype").Property.MozImageRegion | undefined;
            MozMarginEnd?: import("csstype").Property.MarginInlineEnd<string | number> | undefined;
            MozMarginStart?: import("csstype").Property.MarginInlineStart<string | number> | undefined;
            MozOrient?: import("csstype").Property.MozOrient | undefined;
            MozOsxFontSmoothing?: import("csstype").Property.FontSmooth<string | number> | undefined;
            MozOutlineRadiusBottomleft?: import("csstype").Property.MozOutlineRadiusBottomleft<string | number> | undefined;
            MozOutlineRadiusBottomright?: import("csstype").Property.MozOutlineRadiusBottomright<string | number> | undefined;
            MozOutlineRadiusTopleft?: import("csstype").Property.MozOutlineRadiusTopleft<string | number> | undefined;
            MozOutlineRadiusTopright?: import("csstype").Property.MozOutlineRadiusTopright<string | number> | undefined;
            MozPaddingEnd?: import("csstype").Property.PaddingInlineEnd<string | number> | undefined;
            MozPaddingStart?: import("csstype").Property.PaddingInlineStart<string | number> | undefined;
            MozStackSizing?: import("csstype").Property.MozStackSizing | undefined;
            MozTabSize?: import("csstype").Property.TabSize<string | number> | undefined;
            MozTextBlink?: import("csstype").Property.MozTextBlink | undefined;
            MozTextSizeAdjust?: import("csstype").Property.TextSizeAdjust | undefined;
            MozUserFocus?: import("csstype").Property.MozUserFocus | undefined;
            MozUserModify?: import("csstype").Property.MozUserModify | undefined;
            MozUserSelect?: import("csstype").Property.UserSelect | undefined;
            MozWindowDragging?: import("csstype").Property.MozWindowDragging | undefined;
            MozWindowShadow?: import("csstype").Property.MozWindowShadow | undefined;
            msAccelerator?: import("csstype").Property.MsAccelerator | undefined;
            msBlockProgression?: import("csstype").Property.MsBlockProgression | undefined;
            msContentZoomChaining?: import("csstype").Property.MsContentZoomChaining | undefined;
            msContentZoomLimitMax?: import("csstype").Property.MsContentZoomLimitMax | undefined;
            msContentZoomLimitMin?: import("csstype").Property.MsContentZoomLimitMin | undefined;
            msContentZoomSnapPoints?: import("csstype").Property.MsContentZoomSnapPoints | undefined;
            msContentZoomSnapType?: import("csstype").Property.MsContentZoomSnapType | undefined;
            msContentZooming?: import("csstype").Property.MsContentZooming | undefined;
            msFilter?: import("csstype").Property.MsFilter | undefined;
            msFlexDirection?: import("csstype").Property.FlexDirection | undefined;
            msFlexPositive?: import("csstype").Property.FlexGrow | undefined;
            msFlowFrom?: import("csstype").Property.MsFlowFrom | undefined;
            msFlowInto?: import("csstype").Property.MsFlowInto | undefined;
            msGridColumns?: import("csstype").Property.MsGridColumns<string | number> | undefined;
            msGridRows?: import("csstype").Property.MsGridRows<string | number> | undefined;
            msHighContrastAdjust?: import("csstype").Property.MsHighContrastAdjust | undefined;
            msHyphenateLimitChars?: import("csstype").Property.MsHyphenateLimitChars | undefined;
            msHyphenateLimitLines?: import("csstype").Property.MsHyphenateLimitLines | undefined;
            msHyphenateLimitZone?: import("csstype").Property.MsHyphenateLimitZone<string | number> | undefined;
            msHyphens?: import("csstype").Property.Hyphens | undefined;
            msImeAlign?: import("csstype").Property.MsImeAlign | undefined;
            msLineBreak?: import("csstype").Property.LineBreak | undefined;
            msOrder?: import("csstype").Property.Order | undefined;
            msOverflowStyle?: import("csstype").Property.MsOverflowStyle | undefined;
            msOverflowX?: import("csstype").Property.OverflowX | undefined;
            msOverflowY?: import("csstype").Property.OverflowY | undefined;
            msScrollChaining?: import("csstype").Property.MsScrollChaining | undefined;
            msScrollLimitXMax?: import("csstype").Property.MsScrollLimitXMax<string | number> | undefined;
            msScrollLimitXMin?: import("csstype").Property.MsScrollLimitXMin<string | number> | undefined;
            msScrollLimitYMax?: import("csstype").Property.MsScrollLimitYMax<string | number> | undefined;
            msScrollLimitYMin?: import("csstype").Property.MsScrollLimitYMin<string | number> | undefined;
            msScrollRails?: import("csstype").Property.MsScrollRails | undefined;
            msScrollSnapPointsX?: import("csstype").Property.MsScrollSnapPointsX | undefined;
            msScrollSnapPointsY?: import("csstype").Property.MsScrollSnapPointsY | undefined;
            msScrollSnapType?: import("csstype").Property.MsScrollSnapType | undefined;
            msScrollTranslation?: import("csstype").Property.MsScrollTranslation | undefined;
            msScrollbar3dlightColor?: import("csstype").Property.MsScrollbar3dlightColor | undefined;
            msScrollbarArrowColor?: import("csstype").Property.MsScrollbarArrowColor | undefined;
            msScrollbarBaseColor?: import("csstype").Property.MsScrollbarBaseColor | undefined;
            msScrollbarDarkshadowColor?: import("csstype").Property.MsScrollbarDarkshadowColor | undefined;
            msScrollbarFaceColor?: import("csstype").Property.MsScrollbarFaceColor | undefined;
            msScrollbarHighlightColor?: import("csstype").Property.MsScrollbarHighlightColor | undefined;
            msScrollbarShadowColor?: import("csstype").Property.MsScrollbarShadowColor | undefined;
            msScrollbarTrackColor?: import("csstype").Property.MsScrollbarTrackColor | undefined;
            msTextAutospace?: import("csstype").Property.MsTextAutospace | undefined;
            msTextCombineHorizontal?: import("csstype").Property.TextCombineUpright | undefined;
            msTextOverflow?: import("csstype").Property.TextOverflow | undefined;
            msTouchAction?: import("csstype").Property.TouchAction | undefined;
            msTouchSelect?: import("csstype").Property.MsTouchSelect | undefined;
            msTransform?: import("csstype").Property.Transform | undefined;
            msTransformOrigin?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            msTransitionDelay?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            msTransitionDuration?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            msTransitionProperty?: import("csstype").Property.TransitionProperty | undefined;
            msTransitionTimingFunction?: import("csstype").Property.TransitionTimingFunction | undefined;
            msUserSelect?: import("csstype").Property.MsUserSelect | undefined;
            msWordBreak?: import("csstype").Property.WordBreak | undefined;
            msWrapFlow?: import("csstype").Property.MsWrapFlow | undefined;
            msWrapMargin?: import("csstype").Property.MsWrapMargin<string | number> | undefined;
            msWrapThrough?: import("csstype").Property.MsWrapThrough | undefined;
            msWritingMode?: import("csstype").Property.WritingMode | undefined;
            WebkitAlignContent?: import("csstype").Property.AlignContent | undefined;
            WebkitAlignItems?: import("csstype").Property.AlignItems | undefined;
            WebkitAlignSelf?: import("csstype").Property.AlignSelf | undefined;
            WebkitAnimationDelay?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            WebkitAnimationDirection?: import("csstype").Property.AnimationDirection | undefined;
            WebkitAnimationDuration?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            WebkitAnimationFillMode?: import("csstype").Property.AnimationFillMode | undefined;
            WebkitAnimationIterationCount?: import("csstype").Property.AnimationIterationCount | undefined;
            WebkitAnimationName?: import("csstype").Property.AnimationName | undefined;
            WebkitAnimationPlayState?: import("csstype").Property.AnimationPlayState | undefined;
            WebkitAnimationTimingFunction?: import("csstype").Property.AnimationTimingFunction | undefined;
            WebkitAppearance?: import("csstype").Property.WebkitAppearance | undefined;
            WebkitBackdropFilter?: import("csstype").Property.BackdropFilter | undefined;
            WebkitBackfaceVisibility?: import("csstype").Property.BackfaceVisibility | undefined;
            WebkitBackgroundClip?: import("csstype").Property.BackgroundClip | undefined;
            WebkitBackgroundOrigin?: import("csstype").Property.BackgroundOrigin | undefined;
            WebkitBackgroundSize?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            WebkitBorderBeforeColor?: import("csstype").Property.WebkitBorderBeforeColor | undefined;
            WebkitBorderBeforeStyle?: import("csstype").Property.WebkitBorderBeforeStyle | undefined;
            WebkitBorderBeforeWidth?: import("csstype").Property.WebkitBorderBeforeWidth<string | number> | undefined;
            WebkitBorderBottomLeftRadius?: import("csstype").Property.BorderBottomLeftRadius<string | number> | undefined;
            WebkitBorderBottomRightRadius?: import("csstype").Property.BorderBottomRightRadius<string | number> | undefined;
            WebkitBorderImageSlice?: import("csstype").Property.BorderImageSlice | undefined;
            WebkitBorderTopLeftRadius?: import("csstype").Property.BorderTopLeftRadius<string | number> | undefined;
            WebkitBorderTopRightRadius?: import("csstype").Property.BorderTopRightRadius<string | number> | undefined;
            WebkitBoxDecorationBreak?: import("csstype").Property.BoxDecorationBreak | undefined;
            WebkitBoxReflect?: import("csstype").Property.WebkitBoxReflect<string | number> | undefined;
            WebkitBoxShadow?: import("csstype").Property.BoxShadow | undefined;
            WebkitBoxSizing?: import("csstype").Property.BoxSizing | undefined;
            WebkitClipPath?: import("csstype").Property.ClipPath | undefined;
            WebkitColumnCount?: import("csstype").Property.ColumnCount | undefined;
            WebkitColumnFill?: import("csstype").Property.ColumnFill | undefined;
            WebkitColumnRuleColor?: import("csstype").Property.ColumnRuleColor | undefined;
            WebkitColumnRuleStyle?: import("csstype").Property.ColumnRuleStyle | undefined;
            WebkitColumnRuleWidth?: import("csstype").Property.ColumnRuleWidth<string | number> | undefined;
            WebkitColumnSpan?: import("csstype").Property.ColumnSpan | undefined;
            WebkitColumnWidth?: import("csstype").Property.ColumnWidth<string | number> | undefined;
            WebkitFilter?: import("csstype").Property.Filter | undefined;
            WebkitFlexBasis?: import("csstype").Property.FlexBasis<string | number> | undefined;
            WebkitFlexDirection?: import("csstype").Property.FlexDirection | undefined;
            WebkitFlexGrow?: import("csstype").Property.FlexGrow | undefined;
            WebkitFlexShrink?: import("csstype").Property.FlexShrink | undefined;
            WebkitFlexWrap?: import("csstype").Property.FlexWrap | undefined;
            WebkitFontFeatureSettings?: import("csstype").Property.FontFeatureSettings | undefined;
            WebkitFontKerning?: import("csstype").Property.FontKerning | undefined;
            WebkitFontSmoothing?: import("csstype").Property.FontSmooth<string | number> | undefined;
            WebkitFontVariantLigatures?: import("csstype").Property.FontVariantLigatures | undefined;
            WebkitHyphenateCharacter?: import("csstype").Property.HyphenateCharacter | undefined;
            WebkitHyphens?: import("csstype").Property.Hyphens | undefined;
            WebkitInitialLetter?: import("csstype").Property.InitialLetter | undefined;
            WebkitJustifyContent?: import("csstype").Property.JustifyContent | undefined;
            WebkitLineBreak?: import("csstype").Property.LineBreak | undefined;
            WebkitLineClamp?: import("csstype").Property.WebkitLineClamp | undefined;
            WebkitMarginEnd?: import("csstype").Property.MarginInlineEnd<string | number> | undefined;
            WebkitMarginStart?: import("csstype").Property.MarginInlineStart<string | number> | undefined;
            WebkitMaskAttachment?: import("csstype").Property.WebkitMaskAttachment | undefined;
            WebkitMaskBoxImageOutset?: import("csstype").Property.MaskBorderOutset<string | number> | undefined;
            WebkitMaskBoxImageRepeat?: import("csstype").Property.MaskBorderRepeat | undefined;
            WebkitMaskBoxImageSlice?: import("csstype").Property.MaskBorderSlice | undefined;
            WebkitMaskBoxImageSource?: import("csstype").Property.MaskBorderSource | undefined;
            WebkitMaskBoxImageWidth?: import("csstype").Property.MaskBorderWidth<string | number> | undefined;
            WebkitMaskClip?: import("csstype").Property.WebkitMaskClip | undefined;
            WebkitMaskComposite?: import("csstype").Property.WebkitMaskComposite | undefined;
            WebkitMaskImage?: import("csstype").Property.WebkitMaskImage | undefined;
            WebkitMaskOrigin?: import("csstype").Property.WebkitMaskOrigin | undefined;
            WebkitMaskPosition?: import("csstype").Property.WebkitMaskPosition<string | number> | undefined;
            WebkitMaskPositionX?: import("csstype").Property.WebkitMaskPositionX<string | number> | undefined;
            WebkitMaskPositionY?: import("csstype").Property.WebkitMaskPositionY<string | number> | undefined;
            WebkitMaskRepeat?: import("csstype").Property.WebkitMaskRepeat | undefined;
            WebkitMaskRepeatX?: import("csstype").Property.WebkitMaskRepeatX | undefined;
            WebkitMaskRepeatY?: import("csstype").Property.WebkitMaskRepeatY | undefined;
            WebkitMaskSize?: import("csstype").Property.WebkitMaskSize<string | number> | undefined;
            WebkitMaxInlineSize?: import("csstype").Property.MaxInlineSize<string | number> | undefined;
            WebkitOrder?: import("csstype").Property.Order | undefined;
            WebkitOverflowScrolling?: import("csstype").Property.WebkitOverflowScrolling | undefined;
            WebkitPaddingEnd?: import("csstype").Property.PaddingInlineEnd<string | number> | undefined;
            WebkitPaddingStart?: import("csstype").Property.PaddingInlineStart<string | number> | undefined;
            WebkitPerspective?: import("csstype").Property.Perspective<string | number> | undefined;
            WebkitPerspectiveOrigin?: import("csstype").Property.PerspectiveOrigin<string | number> | undefined;
            WebkitPrintColorAdjust?: import("csstype").Property.PrintColorAdjust | undefined;
            WebkitRubyPosition?: import("csstype").Property.RubyPosition | undefined;
            WebkitScrollSnapType?: import("csstype").Property.ScrollSnapType | undefined;
            WebkitShapeMargin?: import("csstype").Property.ShapeMargin<string | number> | undefined;
            WebkitTapHighlightColor?: import("csstype").Property.WebkitTapHighlightColor | undefined;
            WebkitTextCombine?: import("csstype").Property.TextCombineUpright | undefined;
            WebkitTextDecorationColor?: import("csstype").Property.TextDecorationColor | undefined;
            WebkitTextDecorationLine?: import("csstype").Property.TextDecorationLine | undefined;
            WebkitTextDecorationSkip?: import("csstype").Property.TextDecorationSkip | undefined;
            WebkitTextDecorationStyle?: import("csstype").Property.TextDecorationStyle | undefined;
            WebkitTextEmphasisColor?: import("csstype").Property.TextEmphasisColor | undefined;
            WebkitTextEmphasisPosition?: import("csstype").Property.TextEmphasisPosition | undefined;
            WebkitTextEmphasisStyle?: import("csstype").Property.TextEmphasisStyle | undefined;
            WebkitTextFillColor?: import("csstype").Property.WebkitTextFillColor | undefined;
            WebkitTextOrientation?: import("csstype").Property.TextOrientation | undefined;
            WebkitTextSizeAdjust?: import("csstype").Property.TextSizeAdjust | undefined;
            WebkitTextStrokeColor?: import("csstype").Property.WebkitTextStrokeColor | undefined;
            WebkitTextStrokeWidth?: import("csstype").Property.WebkitTextStrokeWidth<string | number> | undefined;
            WebkitTextUnderlinePosition?: import("csstype").Property.TextUnderlinePosition | undefined;
            WebkitTouchCallout?: import("csstype").Property.WebkitTouchCallout | undefined;
            WebkitTransform?: import("csstype").Property.Transform | undefined;
            WebkitTransformOrigin?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            WebkitTransformStyle?: import("csstype").Property.TransformStyle | undefined;
            WebkitTransitionDelay?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            WebkitTransitionDuration?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            WebkitTransitionProperty?: import("csstype").Property.TransitionProperty | undefined;
            WebkitTransitionTimingFunction?: import("csstype").Property.TransitionTimingFunction | undefined;
            WebkitUserModify?: import("csstype").Property.WebkitUserModify | undefined;
            WebkitUserSelect?: import("csstype").Property.UserSelect | undefined;
            WebkitWritingMode?: import("csstype").Property.WritingMode | undefined;
            MozAnimation?: import("csstype").Property.Animation<string & {}> | undefined;
            MozBorderImage?: import("csstype").Property.BorderImage | undefined;
            MozColumnRule?: import("csstype").Property.ColumnRule<string | number> | undefined;
            MozColumns?: import("csstype").Property.Columns<string | number> | undefined;
            MozOutlineRadius?: import("csstype").Property.MozOutlineRadius<string | number> | undefined;
            msContentZoomLimit?: import("csstype").Property.MsContentZoomLimit | undefined;
            msContentZoomSnap?: import("csstype").Property.MsContentZoomSnap | undefined;
            msFlex?: import("csstype").Property.Flex<string | number> | undefined;
            msScrollLimit?: import("csstype").Property.MsScrollLimit | undefined;
            msScrollSnapX?: import("csstype").Property.MsScrollSnapX | undefined;
            msScrollSnapY?: import("csstype").Property.MsScrollSnapY | undefined;
            msTransition?: import("csstype").Property.Transition<string & {}> | undefined;
            WebkitAnimation?: import("csstype").Property.Animation<string & {}> | undefined;
            WebkitBorderBefore?: import("csstype").Property.WebkitBorderBefore<string | number> | undefined;
            WebkitBorderImage?: import("csstype").Property.BorderImage | undefined;
            WebkitBorderRadius?: import("csstype").Property.BorderRadius<string | number> | undefined;
            WebkitColumnRule?: import("csstype").Property.ColumnRule<string | number> | undefined;
            WebkitColumns?: import("csstype").Property.Columns<string | number> | undefined;
            WebkitFlex?: import("csstype").Property.Flex<string | number> | undefined;
            WebkitFlexFlow?: import("csstype").Property.FlexFlow | undefined;
            WebkitMask?: import("csstype").Property.WebkitMask<string | number> | undefined;
            WebkitMaskBoxImage?: import("csstype").Property.MaskBorder | undefined;
            WebkitTextEmphasis?: import("csstype").Property.TextEmphasis | undefined;
            WebkitTextStroke?: import("csstype").Property.WebkitTextStroke<string | number> | undefined;
            WebkitTransition?: import("csstype").Property.Transition<string & {}> | undefined;
            azimuth?: import("csstype").Property.Azimuth | undefined;
            boxAlign?: import("csstype").Property.BoxAlign | undefined;
            boxDirection?: import("csstype").Property.BoxDirection | undefined;
            boxFlex?: import("csstype").Property.BoxFlex | undefined;
            boxFlexGroup?: import("csstype").Property.BoxFlexGroup | undefined;
            boxLines?: import("csstype").Property.BoxLines | undefined;
            boxOrdinalGroup?: import("csstype").Property.BoxOrdinalGroup | undefined;
            boxOrient?: import("csstype").Property.BoxOrient | undefined;
            boxPack?: import("csstype").Property.BoxPack | undefined;
            clip?: import("csstype").Property.Clip | undefined;
            gridColumnGap?: import("csstype").Property.GridColumnGap<string | number> | undefined;
            gridGap?: import("csstype").Property.GridGap<string | number> | undefined;
            gridRowGap?: import("csstype").Property.GridRowGap<string | number> | undefined;
            imeMode?: import("csstype").Property.ImeMode | undefined;
            offsetBlock?: import("csstype").Property.InsetBlock<string | number> | undefined;
            offsetBlockEnd?: import("csstype").Property.InsetBlockEnd<string | number> | undefined;
            offsetBlockStart?: import("csstype").Property.InsetBlockStart<string | number> | undefined;
            offsetInline?: import("csstype").Property.InsetInline<string | number> | undefined;
            offsetInlineEnd?: import("csstype").Property.InsetInlineEnd<string | number> | undefined;
            offsetInlineStart?: import("csstype").Property.InsetInlineStart<string | number> | undefined;
            scrollSnapCoordinate?: import("csstype").Property.ScrollSnapCoordinate<string | number> | undefined;
            scrollSnapDestination?: import("csstype").Property.ScrollSnapDestination<string | number> | undefined;
            scrollSnapPointsX?: import("csstype").Property.ScrollSnapPointsX | undefined;
            scrollSnapPointsY?: import("csstype").Property.ScrollSnapPointsY | undefined;
            scrollSnapTypeX?: import("csstype").Property.ScrollSnapTypeX | undefined;
            scrollSnapTypeY?: import("csstype").Property.ScrollSnapTypeY | undefined;
            KhtmlBoxAlign?: import("csstype").Property.BoxAlign | undefined;
            KhtmlBoxDirection?: import("csstype").Property.BoxDirection | undefined;
            KhtmlBoxFlex?: import("csstype").Property.BoxFlex | undefined;
            KhtmlBoxFlexGroup?: import("csstype").Property.BoxFlexGroup | undefined;
            KhtmlBoxLines?: import("csstype").Property.BoxLines | undefined;
            KhtmlBoxOrdinalGroup?: import("csstype").Property.BoxOrdinalGroup | undefined;
            KhtmlBoxOrient?: import("csstype").Property.BoxOrient | undefined;
            KhtmlBoxPack?: import("csstype").Property.BoxPack | undefined;
            KhtmlLineBreak?: import("csstype").Property.LineBreak | undefined;
            KhtmlOpacity?: import("csstype").Property.Opacity | undefined;
            KhtmlUserSelect?: import("csstype").Property.UserSelect | undefined;
            MozBackfaceVisibility?: import("csstype").Property.BackfaceVisibility | undefined;
            MozBackgroundClip?: import("csstype").Property.BackgroundClip | undefined;
            MozBackgroundInlinePolicy?: import("csstype").Property.BoxDecorationBreak | undefined;
            MozBackgroundOrigin?: import("csstype").Property.BackgroundOrigin | undefined;
            MozBackgroundSize?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            MozBorderRadius?: import("csstype").Property.BorderRadius<string | number> | undefined;
            MozBorderRadiusBottomleft?: import("csstype").Property.BorderBottomLeftRadius<string | number> | undefined;
            MozBorderRadiusBottomright?: import("csstype").Property.BorderBottomRightRadius<string | number> | undefined;
            MozBorderRadiusTopleft?: import("csstype").Property.BorderTopLeftRadius<string | number> | undefined;
            MozBorderRadiusTopright?: import("csstype").Property.BorderTopRightRadius<string | number> | undefined;
            MozBoxAlign?: import("csstype").Property.BoxAlign | undefined;
            MozBoxDirection?: import("csstype").Property.BoxDirection | undefined;
            MozBoxFlex?: import("csstype").Property.BoxFlex | undefined;
            MozBoxOrdinalGroup?: import("csstype").Property.BoxOrdinalGroup | undefined;
            MozBoxOrient?: import("csstype").Property.BoxOrient | undefined;
            MozBoxPack?: import("csstype").Property.BoxPack | undefined;
            MozBoxShadow?: import("csstype").Property.BoxShadow | undefined;
            MozFloatEdge?: import("csstype").Property.MozFloatEdge | undefined;
            MozForceBrokenImageIcon?: import("csstype").Property.MozForceBrokenImageIcon | undefined;
            MozOpacity?: import("csstype").Property.Opacity | undefined;
            MozOutline?: import("csstype").Property.Outline<string | number> | undefined;
            MozOutlineColor?: import("csstype").Property.OutlineColor | undefined;
            MozOutlineStyle?: import("csstype").Property.OutlineStyle | undefined;
            MozOutlineWidth?: import("csstype").Property.OutlineWidth<string | number> | undefined;
            MozPerspective?: import("csstype").Property.Perspective<string | number> | undefined;
            MozPerspectiveOrigin?: import("csstype").Property.PerspectiveOrigin<string | number> | undefined;
            MozTextAlignLast?: import("csstype").Property.TextAlignLast | undefined;
            MozTextDecorationColor?: import("csstype").Property.TextDecorationColor | undefined;
            MozTextDecorationLine?: import("csstype").Property.TextDecorationLine | undefined;
            MozTextDecorationStyle?: import("csstype").Property.TextDecorationStyle | undefined;
            MozTransform?: import("csstype").Property.Transform | undefined;
            MozTransformOrigin?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            MozTransformStyle?: import("csstype").Property.TransformStyle | undefined;
            MozTransition?: import("csstype").Property.Transition<string & {}> | undefined;
            MozTransitionDelay?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            MozTransitionDuration?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            MozTransitionProperty?: import("csstype").Property.TransitionProperty | undefined;
            MozTransitionTimingFunction?: import("csstype").Property.TransitionTimingFunction | undefined;
            MozUserInput?: import("csstype").Property.MozUserInput | undefined;
            msImeMode?: import("csstype").Property.ImeMode | undefined;
            OAnimation?: import("csstype").Property.Animation<string & {}> | undefined;
            OAnimationDelay?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            OAnimationDirection?: import("csstype").Property.AnimationDirection | undefined;
            OAnimationDuration?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            OAnimationFillMode?: import("csstype").Property.AnimationFillMode | undefined;
            OAnimationIterationCount?: import("csstype").Property.AnimationIterationCount | undefined;
            OAnimationName?: import("csstype").Property.AnimationName | undefined;
            OAnimationPlayState?: import("csstype").Property.AnimationPlayState | undefined;
            OAnimationTimingFunction?: import("csstype").Property.AnimationTimingFunction | undefined;
            OBackgroundSize?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            OBorderImage?: import("csstype").Property.BorderImage | undefined;
            OObjectFit?: import("csstype").Property.ObjectFit | undefined;
            OObjectPosition?: import("csstype").Property.ObjectPosition<string | number> | undefined;
            OTabSize?: import("csstype").Property.TabSize<string | number> | undefined;
            OTextOverflow?: import("csstype").Property.TextOverflow | undefined;
            OTransform?: import("csstype").Property.Transform | undefined;
            OTransformOrigin?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            OTransition?: import("csstype").Property.Transition<string & {}> | undefined;
            OTransitionDelay?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            OTransitionDuration?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            OTransitionProperty?: import("csstype").Property.TransitionProperty | undefined;
            OTransitionTimingFunction?: import("csstype").Property.TransitionTimingFunction | undefined;
            WebkitBoxAlign?: import("csstype").Property.BoxAlign | undefined;
            WebkitBoxDirection?: import("csstype").Property.BoxDirection | undefined;
            WebkitBoxFlex?: import("csstype").Property.BoxFlex | undefined;
            WebkitBoxFlexGroup?: import("csstype").Property.BoxFlexGroup | undefined;
            WebkitBoxLines?: import("csstype").Property.BoxLines | undefined;
            WebkitBoxOrdinalGroup?: import("csstype").Property.BoxOrdinalGroup | undefined;
            WebkitBoxOrient?: import("csstype").Property.BoxOrient | undefined;
            WebkitBoxPack?: import("csstype").Property.BoxPack | undefined;
            alignmentBaseline?: import("csstype").Property.AlignmentBaseline | undefined;
            baselineShift?: import("csstype").Property.BaselineShift<string | number> | undefined;
            clipRule?: import("csstype").Property.ClipRule | undefined;
            colorInterpolation?: import("csstype").Property.ColorInterpolation | undefined;
            colorRendering?: import("csstype").Property.ColorRendering | undefined;
            dominantBaseline?: import("csstype").Property.DominantBaseline | undefined;
            fill?: import("csstype").Property.Fill | undefined;
            fillOpacity?: import("csstype").Property.FillOpacity | undefined;
            fillRule?: import("csstype").Property.FillRule | undefined;
            floodColor?: import("csstype").Property.FloodColor | undefined;
            floodOpacity?: import("csstype").Property.FloodOpacity | undefined;
            glyphOrientationVertical?: import("csstype").Property.GlyphOrientationVertical | undefined;
            lightingColor?: import("csstype").Property.LightingColor | undefined;
            marker?: import("csstype").Property.Marker | undefined;
            markerEnd?: import("csstype").Property.MarkerEnd | undefined;
            markerMid?: import("csstype").Property.MarkerMid | undefined;
            markerStart?: import("csstype").Property.MarkerStart | undefined;
            shapeRendering?: import("csstype").Property.ShapeRendering | undefined;
            stopColor?: import("csstype").Property.StopColor | undefined;
            stopOpacity?: import("csstype").Property.StopOpacity | undefined;
            stroke?: import("csstype").Property.Stroke | undefined;
            strokeDasharray?: import("csstype").Property.StrokeDasharray<string | number> | undefined;
            strokeDashoffset?: import("csstype").Property.StrokeDashoffset<string | number> | undefined;
            strokeLinecap?: import("csstype").Property.StrokeLinecap | undefined;
            strokeLinejoin?: import("csstype").Property.StrokeLinejoin | undefined;
            strokeMiterlimit?: import("csstype").Property.StrokeMiterlimit | undefined;
            strokeOpacity?: import("csstype").Property.StrokeOpacity | undefined;
            strokeWidth?: import("csstype").Property.StrokeWidth<string | number> | undefined;
            textAnchor?: import("csstype").Property.TextAnchor | undefined;
            vectorEffect?: import("csstype").Property.VectorEffect | undefined;
            "accent-color"?: import("csstype").Property.AccentColor | undefined;
            "align-content"?: import("csstype").Property.AlignContent | undefined;
            "align-items"?: import("csstype").Property.AlignItems | undefined;
            "align-self"?: import("csstype").Property.AlignSelf | undefined;
            "align-tracks"?: import("csstype").Property.AlignTracks | undefined;
            "animation-composition"?: import("csstype").Property.AnimationComposition | undefined;
            "animation-delay"?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            "animation-direction"?: import("csstype").Property.AnimationDirection | undefined;
            "animation-duration"?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            "animation-fill-mode"?: import("csstype").Property.AnimationFillMode | undefined;
            "animation-iteration-count"?: import("csstype").Property.AnimationIterationCount | undefined;
            "animation-name"?: import("csstype").Property.AnimationName | undefined;
            "animation-play-state"?: import("csstype").Property.AnimationPlayState | undefined;
            "animation-range-end"?: import("csstype").Property.AnimationRangeEnd<string | number> | undefined;
            "animation-range-start"?: import("csstype").Property.AnimationRangeStart<string | number> | undefined;
            "animation-timeline"?: import("csstype").Property.AnimationTimeline | undefined;
            "animation-timing-function"?: import("csstype").Property.AnimationTimingFunction | undefined;
            "aspect-ratio"?: import("csstype").Property.AspectRatio | undefined;
            "backdrop-filter"?: import("csstype").Property.BackdropFilter | undefined;
            "backface-visibility"?: import("csstype").Property.BackfaceVisibility | undefined;
            "background-attachment"?: import("csstype").Property.BackgroundAttachment | undefined;
            "background-blend-mode"?: import("csstype").Property.BackgroundBlendMode | undefined;
            "background-clip"?: import("csstype").Property.BackgroundClip | undefined;
            "background-color"?: import("csstype").Property.BackgroundColor | undefined;
            "background-image"?: import("csstype").Property.BackgroundImage | undefined;
            "background-origin"?: import("csstype").Property.BackgroundOrigin | undefined;
            "background-position-x"?: import("csstype").Property.BackgroundPositionX<string | number> | undefined;
            "background-position-y"?: import("csstype").Property.BackgroundPositionY<string | number> | undefined;
            "background-repeat"?: import("csstype").Property.BackgroundRepeat | undefined;
            "background-size"?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            "block-overflow"?: import("csstype").Property.BlockOverflow | undefined;
            "block-size"?: import("csstype").Property.BlockSize<string | number> | undefined;
            "border-block-color"?: import("csstype").Property.BorderBlockColor | undefined;
            "border-block-end-color"?: import("csstype").Property.BorderBlockEndColor | undefined;
            "border-block-end-style"?: import("csstype").Property.BorderBlockEndStyle | undefined;
            "border-block-end-width"?: import("csstype").Property.BorderBlockEndWidth<string | number> | undefined;
            "border-block-start-color"?: import("csstype").Property.BorderBlockStartColor | undefined;
            "border-block-start-style"?: import("csstype").Property.BorderBlockStartStyle | undefined;
            "border-block-start-width"?: import("csstype").Property.BorderBlockStartWidth<string | number> | undefined;
            "border-block-style"?: import("csstype").Property.BorderBlockStyle | undefined;
            "border-block-width"?: import("csstype").Property.BorderBlockWidth<string | number> | undefined;
            "border-bottom-color"?: import("csstype").Property.BorderBottomColor | undefined;
            "border-bottom-left-radius"?: import("csstype").Property.BorderBottomLeftRadius<string | number> | undefined;
            "border-bottom-right-radius"?: import("csstype").Property.BorderBottomRightRadius<string | number> | undefined;
            "border-bottom-style"?: import("csstype").Property.BorderBottomStyle | undefined;
            "border-bottom-width"?: import("csstype").Property.BorderBottomWidth<string | number> | undefined;
            "border-collapse"?: import("csstype").Property.BorderCollapse | undefined;
            "border-end-end-radius"?: import("csstype").Property.BorderEndEndRadius<string | number> | undefined;
            "border-end-start-radius"?: import("csstype").Property.BorderEndStartRadius<string | number> | undefined;
            "border-image-outset"?: import("csstype").Property.BorderImageOutset<string | number> | undefined;
            "border-image-repeat"?: import("csstype").Property.BorderImageRepeat | undefined;
            "border-image-slice"?: import("csstype").Property.BorderImageSlice | undefined;
            "border-image-source"?: import("csstype").Property.BorderImageSource | undefined;
            "border-image-width"?: import("csstype").Property.BorderImageWidth<string | number> | undefined;
            "border-inline-color"?: import("csstype").Property.BorderInlineColor | undefined;
            "border-inline-end-color"?: import("csstype").Property.BorderInlineEndColor | undefined;
            "border-inline-end-style"?: import("csstype").Property.BorderInlineEndStyle | undefined;
            "border-inline-end-width"?: import("csstype").Property.BorderInlineEndWidth<string | number> | undefined;
            "border-inline-start-color"?: import("csstype").Property.BorderInlineStartColor | undefined;
            "border-inline-start-style"?: import("csstype").Property.BorderInlineStartStyle | undefined;
            "border-inline-start-width"?: import("csstype").Property.BorderInlineStartWidth<string | number> | undefined;
            "border-inline-style"?: import("csstype").Property.BorderInlineStyle | undefined;
            "border-inline-width"?: import("csstype").Property.BorderInlineWidth<string | number> | undefined;
            "border-left-color"?: import("csstype").Property.BorderLeftColor | undefined;
            "border-left-style"?: import("csstype").Property.BorderLeftStyle | undefined;
            "border-left-width"?: import("csstype").Property.BorderLeftWidth<string | number> | undefined;
            "border-right-color"?: import("csstype").Property.BorderRightColor | undefined;
            "border-right-style"?: import("csstype").Property.BorderRightStyle | undefined;
            "border-right-width"?: import("csstype").Property.BorderRightWidth<string | number> | undefined;
            "border-spacing"?: import("csstype").Property.BorderSpacing<string | number> | undefined;
            "border-start-end-radius"?: import("csstype").Property.BorderStartEndRadius<string | number> | undefined;
            "border-start-start-radius"?: import("csstype").Property.BorderStartStartRadius<string | number> | undefined;
            "border-top-color"?: import("csstype").Property.BorderTopColor | undefined;
            "border-top-left-radius"?: import("csstype").Property.BorderTopLeftRadius<string | number> | undefined;
            "border-top-right-radius"?: import("csstype").Property.BorderTopRightRadius<string | number> | undefined;
            "border-top-style"?: import("csstype").Property.BorderTopStyle | undefined;
            "border-top-width"?: import("csstype").Property.BorderTopWidth<string | number> | undefined;
            "box-decoration-break"?: import("csstype").Property.BoxDecorationBreak | undefined;
            "box-shadow"?: import("csstype").Property.BoxShadow | undefined;
            "box-sizing"?: import("csstype").Property.BoxSizing | undefined;
            "break-after"?: import("csstype").Property.BreakAfter | undefined;
            "break-before"?: import("csstype").Property.BreakBefore | undefined;
            "break-inside"?: import("csstype").Property.BreakInside | undefined;
            "caption-side"?: import("csstype").Property.CaptionSide | undefined;
            "caret-color"?: import("csstype").Property.CaretColor | undefined;
            "caret-shape"?: import("csstype").Property.CaretShape | undefined;
            "clip-path"?: import("csstype").Property.ClipPath | undefined;
            "color-adjust"?: import("csstype").Property.PrintColorAdjust | undefined;
            "color-scheme"?: import("csstype").Property.ColorScheme | undefined;
            "column-count"?: import("csstype").Property.ColumnCount | undefined;
            "column-fill"?: import("csstype").Property.ColumnFill | undefined;
            "column-gap"?: import("csstype").Property.ColumnGap<string | number> | undefined;
            "column-rule-color"?: import("csstype").Property.ColumnRuleColor | undefined;
            "column-rule-style"?: import("csstype").Property.ColumnRuleStyle | undefined;
            "column-rule-width"?: import("csstype").Property.ColumnRuleWidth<string | number> | undefined;
            "column-span"?: import("csstype").Property.ColumnSpan | undefined;
            "column-width"?: import("csstype").Property.ColumnWidth<string | number> | undefined;
            "contain-intrinsic-block-size"?: import("csstype").Property.ContainIntrinsicBlockSize<string | number> | undefined;
            "contain-intrinsic-height"?: import("csstype").Property.ContainIntrinsicHeight<string | number> | undefined;
            "contain-intrinsic-inline-size"?: import("csstype").Property.ContainIntrinsicInlineSize<string | number> | undefined;
            "contain-intrinsic-width"?: import("csstype").Property.ContainIntrinsicWidth<string | number> | undefined;
            "container-name"?: import("csstype").Property.ContainerName | undefined;
            "container-type"?: import("csstype").Property.ContainerType | undefined;
            "content-visibility"?: import("csstype").Property.ContentVisibility | undefined;
            "counter-increment"?: import("csstype").Property.CounterIncrement | undefined;
            "counter-reset"?: import("csstype").Property.CounterReset | undefined;
            "counter-set"?: import("csstype").Property.CounterSet | undefined;
            "empty-cells"?: import("csstype").Property.EmptyCells | undefined;
            "flex-basis"?: import("csstype").Property.FlexBasis<string | number> | undefined;
            "flex-direction"?: import("csstype").Property.FlexDirection | undefined;
            "flex-grow"?: import("csstype").Property.FlexGrow | undefined;
            "flex-shrink"?: import("csstype").Property.FlexShrink | undefined;
            "flex-wrap"?: import("csstype").Property.FlexWrap | undefined;
            "font-family"?: import("csstype").Property.FontFamily | undefined;
            "font-feature-settings"?: import("csstype").Property.FontFeatureSettings | undefined;
            "font-kerning"?: import("csstype").Property.FontKerning | undefined;
            "font-language-override"?: import("csstype").Property.FontLanguageOverride | undefined;
            "font-optical-sizing"?: import("csstype").Property.FontOpticalSizing | undefined;
            "font-palette"?: import("csstype").Property.FontPalette | undefined;
            "font-size"?: import("csstype").Property.FontSize<string | number> | undefined;
            "font-size-adjust"?: import("csstype").Property.FontSizeAdjust | undefined;
            "font-smooth"?: import("csstype").Property.FontSmooth<string | number> | undefined;
            "font-stretch"?: import("csstype").Property.FontStretch | undefined;
            "font-style"?: import("csstype").Property.FontStyle | undefined;
            "font-synthesis"?: import("csstype").Property.FontSynthesis | undefined;
            "font-synthesis-position"?: import("csstype").Property.FontSynthesisPosition | undefined;
            "font-synthesis-small-caps"?: import("csstype").Property.FontSynthesisSmallCaps | undefined;
            "font-synthesis-style"?: import("csstype").Property.FontSynthesisStyle | undefined;
            "font-synthesis-weight"?: import("csstype").Property.FontSynthesisWeight | undefined;
            "font-variant"?: import("csstype").Property.FontVariant | undefined;
            "font-variant-alternates"?: import("csstype").Property.FontVariantAlternates | undefined;
            "font-variant-caps"?: import("csstype").Property.FontVariantCaps | undefined;
            "font-variant-east-asian"?: import("csstype").Property.FontVariantEastAsian | undefined;
            "font-variant-emoji"?: import("csstype").Property.FontVariantEmoji | undefined;
            "font-variant-ligatures"?: import("csstype").Property.FontVariantLigatures | undefined;
            "font-variant-numeric"?: import("csstype").Property.FontVariantNumeric | undefined;
            "font-variant-position"?: import("csstype").Property.FontVariantPosition | undefined;
            "font-variation-settings"?: import("csstype").Property.FontVariationSettings | undefined;
            "font-weight"?: import("csstype").Property.FontWeight | undefined;
            "forced-color-adjust"?: import("csstype").Property.ForcedColorAdjust | undefined;
            "grid-auto-columns"?: import("csstype").Property.GridAutoColumns<string | number> | undefined;
            "grid-auto-flow"?: import("csstype").Property.GridAutoFlow | undefined;
            "grid-auto-rows"?: import("csstype").Property.GridAutoRows<string | number> | undefined;
            "grid-column-end"?: import("csstype").Property.GridColumnEnd | undefined;
            "grid-column-start"?: import("csstype").Property.GridColumnStart | undefined;
            "grid-row-end"?: import("csstype").Property.GridRowEnd | undefined;
            "grid-row-start"?: import("csstype").Property.GridRowStart | undefined;
            "grid-template-areas"?: import("csstype").Property.GridTemplateAreas | undefined;
            "grid-template-columns"?: import("csstype").Property.GridTemplateColumns<string | number> | undefined;
            "grid-template-rows"?: import("csstype").Property.GridTemplateRows<string | number> | undefined;
            "hanging-punctuation"?: import("csstype").Property.HangingPunctuation | undefined;
            "hyphenate-character"?: import("csstype").Property.HyphenateCharacter | undefined;
            "hyphenate-limit-chars"?: import("csstype").Property.HyphenateLimitChars | undefined;
            "image-orientation"?: import("csstype").Property.ImageOrientation | undefined;
            "image-rendering"?: import("csstype").Property.ImageRendering | undefined;
            "image-resolution"?: import("csstype").Property.ImageResolution | undefined;
            "initial-letter"?: import("csstype").Property.InitialLetter | undefined;
            "inline-size"?: import("csstype").Property.InlineSize<string | number> | undefined;
            "input-security"?: import("csstype").Property.InputSecurity | undefined;
            "inset-block-end"?: import("csstype").Property.InsetBlockEnd<string | number> | undefined;
            "inset-block-start"?: import("csstype").Property.InsetBlockStart<string | number> | undefined;
            "inset-inline-end"?: import("csstype").Property.InsetInlineEnd<string | number> | undefined;
            "inset-inline-start"?: import("csstype").Property.InsetInlineStart<string | number> | undefined;
            "justify-content"?: import("csstype").Property.JustifyContent | undefined;
            "justify-items"?: import("csstype").Property.JustifyItems | undefined;
            "justify-self"?: import("csstype").Property.JustifySelf | undefined;
            "justify-tracks"?: import("csstype").Property.JustifyTracks | undefined;
            "letter-spacing"?: import("csstype").Property.LetterSpacing<string | number> | undefined;
            "line-break"?: import("csstype").Property.LineBreak | undefined;
            "line-height"?: import("csstype").Property.LineHeight<string | number> | undefined;
            "line-height-step"?: import("csstype").Property.LineHeightStep<string | number> | undefined;
            "list-style-image"?: import("csstype").Property.ListStyleImage | undefined;
            "list-style-position"?: import("csstype").Property.ListStylePosition | undefined;
            "list-style-type"?: import("csstype").Property.ListStyleType | undefined;
            "margin-block-end"?: import("csstype").Property.MarginBlockEnd<string | number> | undefined;
            "margin-block-start"?: import("csstype").Property.MarginBlockStart<string | number> | undefined;
            "margin-bottom"?: import("csstype").Property.MarginBottom<string | number> | undefined;
            "margin-inline-end"?: import("csstype").Property.MarginInlineEnd<string | number> | undefined;
            "margin-inline-start"?: import("csstype").Property.MarginInlineStart<string | number> | undefined;
            "margin-left"?: import("csstype").Property.MarginLeft<string | number> | undefined;
            "margin-right"?: import("csstype").Property.MarginRight<string | number> | undefined;
            "margin-top"?: import("csstype").Property.MarginTop<string | number> | undefined;
            "margin-trim"?: import("csstype").Property.MarginTrim | undefined;
            "mask-border-mode"?: import("csstype").Property.MaskBorderMode | undefined;
            "mask-border-outset"?: import("csstype").Property.MaskBorderOutset<string | number> | undefined;
            "mask-border-repeat"?: import("csstype").Property.MaskBorderRepeat | undefined;
            "mask-border-slice"?: import("csstype").Property.MaskBorderSlice | undefined;
            "mask-border-source"?: import("csstype").Property.MaskBorderSource | undefined;
            "mask-border-width"?: import("csstype").Property.MaskBorderWidth<string | number> | undefined;
            "mask-clip"?: import("csstype").Property.MaskClip | undefined;
            "mask-composite"?: import("csstype").Property.MaskComposite | undefined;
            "mask-image"?: import("csstype").Property.MaskImage | undefined;
            "mask-mode"?: import("csstype").Property.MaskMode | undefined;
            "mask-origin"?: import("csstype").Property.MaskOrigin | undefined;
            "mask-position"?: import("csstype").Property.MaskPosition<string | number> | undefined;
            "mask-repeat"?: import("csstype").Property.MaskRepeat | undefined;
            "mask-size"?: import("csstype").Property.MaskSize<string | number> | undefined;
            "mask-type"?: import("csstype").Property.MaskType | undefined;
            "masonry-auto-flow"?: import("csstype").Property.MasonryAutoFlow | undefined;
            "math-depth"?: import("csstype").Property.MathDepth | undefined;
            "math-shift"?: import("csstype").Property.MathShift | undefined;
            "math-style"?: import("csstype").Property.MathStyle | undefined;
            "max-block-size"?: import("csstype").Property.MaxBlockSize<string | number> | undefined;
            "max-height"?: import("csstype").Property.MaxHeight<string | number> | undefined;
            "max-inline-size"?: import("csstype").Property.MaxInlineSize<string | number> | undefined;
            "max-lines"?: import("csstype").Property.MaxLines | undefined;
            "max-width"?: import("csstype").Property.MaxWidth<string | number> | undefined;
            "min-block-size"?: import("csstype").Property.MinBlockSize<string | number> | undefined;
            "min-height"?: import("csstype").Property.MinHeight<string | number> | undefined;
            "min-inline-size"?: import("csstype").Property.MinInlineSize<string | number> | undefined;
            "min-width"?: import("csstype").Property.MinWidth<string | number> | undefined;
            "mix-blend-mode"?: import("csstype").Property.MixBlendMode | undefined;
            "motion-distance"?: import("csstype").Property.OffsetDistance<string | number> | undefined;
            "motion-path"?: import("csstype").Property.OffsetPath | undefined;
            "motion-rotation"?: import("csstype").Property.OffsetRotate | undefined;
            "object-fit"?: import("csstype").Property.ObjectFit | undefined;
            "object-position"?: import("csstype").Property.ObjectPosition<string | number> | undefined;
            "offset-anchor"?: import("csstype").Property.OffsetAnchor<string | number> | undefined;
            "offset-distance"?: import("csstype").Property.OffsetDistance<string | number> | undefined;
            "offset-path"?: import("csstype").Property.OffsetPath | undefined;
            "offset-position"?: import("csstype").Property.OffsetPosition<string | number> | undefined;
            "offset-rotate"?: import("csstype").Property.OffsetRotate | undefined;
            "offset-rotation"?: import("csstype").Property.OffsetRotate | undefined;
            "outline-color"?: import("csstype").Property.OutlineColor | undefined;
            "outline-offset"?: import("csstype").Property.OutlineOffset<string | number> | undefined;
            "outline-style"?: import("csstype").Property.OutlineStyle | undefined;
            "outline-width"?: import("csstype").Property.OutlineWidth<string | number> | undefined;
            "overflow-anchor"?: import("csstype").Property.OverflowAnchor | undefined;
            "overflow-block"?: import("csstype").Property.OverflowBlock | undefined;
            "overflow-clip-box"?: import("csstype").Property.OverflowClipBox | undefined;
            "overflow-clip-margin"?: import("csstype").Property.OverflowClipMargin<string | number> | undefined;
            "overflow-inline"?: import("csstype").Property.OverflowInline | undefined;
            "overflow-wrap"?: import("csstype").Property.OverflowWrap | undefined;
            "overflow-x"?: import("csstype").Property.OverflowX | undefined;
            "overflow-y"?: import("csstype").Property.OverflowY | undefined;
            "overscroll-behavior-block"?: import("csstype").Property.OverscrollBehaviorBlock | undefined;
            "overscroll-behavior-inline"?: import("csstype").Property.OverscrollBehaviorInline | undefined;
            "overscroll-behavior-x"?: import("csstype").Property.OverscrollBehaviorX | undefined;
            "overscroll-behavior-y"?: import("csstype").Property.OverscrollBehaviorY | undefined;
            "padding-block-end"?: import("csstype").Property.PaddingBlockEnd<string | number> | undefined;
            "padding-block-start"?: import("csstype").Property.PaddingBlockStart<string | number> | undefined;
            "padding-bottom"?: import("csstype").Property.PaddingBottom<string | number> | undefined;
            "padding-inline-end"?: import("csstype").Property.PaddingInlineEnd<string | number> | undefined;
            "padding-inline-start"?: import("csstype").Property.PaddingInlineStart<string | number> | undefined;
            "padding-left"?: import("csstype").Property.PaddingLeft<string | number> | undefined;
            "padding-right"?: import("csstype").Property.PaddingRight<string | number> | undefined;
            "padding-top"?: import("csstype").Property.PaddingTop<string | number> | undefined;
            "page-break-after"?: import("csstype").Property.PageBreakAfter | undefined;
            "page-break-before"?: import("csstype").Property.PageBreakBefore | undefined;
            "page-break-inside"?: import("csstype").Property.PageBreakInside | undefined;
            "paint-order"?: import("csstype").Property.PaintOrder | undefined;
            "perspective-origin"?: import("csstype").Property.PerspectiveOrigin<string | number> | undefined;
            "pointer-events"?: import("csstype").Property.PointerEvents | undefined;
            "print-color-adjust"?: import("csstype").Property.PrintColorAdjust | undefined;
            "row-gap"?: import("csstype").Property.RowGap<string | number> | undefined;
            "ruby-align"?: import("csstype").Property.RubyAlign | undefined;
            "ruby-merge"?: import("csstype").Property.RubyMerge | undefined;
            "ruby-position"?: import("csstype").Property.RubyPosition | undefined;
            "scroll-behavior"?: import("csstype").Property.ScrollBehavior | undefined;
            "scroll-margin-block-end"?: import("csstype").Property.ScrollMarginBlockEnd<string | number> | undefined;
            "scroll-margin-block-start"?: import("csstype").Property.ScrollMarginBlockStart<string | number> | undefined;
            "scroll-margin-bottom"?: import("csstype").Property.ScrollMarginBottom<string | number> | undefined;
            "scroll-margin-inline-end"?: import("csstype").Property.ScrollMarginInlineEnd<string | number> | undefined;
            "scroll-margin-inline-start"?: import("csstype").Property.ScrollMarginInlineStart<string | number> | undefined;
            "scroll-margin-left"?: import("csstype").Property.ScrollMarginLeft<string | number> | undefined;
            "scroll-margin-right"?: import("csstype").Property.ScrollMarginRight<string | number> | undefined;
            "scroll-margin-top"?: import("csstype").Property.ScrollMarginTop<string | number> | undefined;
            "scroll-padding-block-end"?: import("csstype").Property.ScrollPaddingBlockEnd<string | number> | undefined;
            "scroll-padding-block-start"?: import("csstype").Property.ScrollPaddingBlockStart<string | number> | undefined;
            "scroll-padding-bottom"?: import("csstype").Property.ScrollPaddingBottom<string | number> | undefined;
            "scroll-padding-inline-end"?: import("csstype").Property.ScrollPaddingInlineEnd<string | number> | undefined;
            "scroll-padding-inline-start"?: import("csstype").Property.ScrollPaddingInlineStart<string | number> | undefined;
            "scroll-padding-left"?: import("csstype").Property.ScrollPaddingLeft<string | number> | undefined;
            "scroll-padding-right"?: import("csstype").Property.ScrollPaddingRight<string | number> | undefined;
            "scroll-padding-top"?: import("csstype").Property.ScrollPaddingTop<string | number> | undefined;
            "scroll-snap-align"?: import("csstype").Property.ScrollSnapAlign | undefined;
            "scroll-snap-margin-bottom"?: import("csstype").Property.ScrollMarginBottom<string | number> | undefined;
            "scroll-snap-margin-left"?: import("csstype").Property.ScrollMarginLeft<string | number> | undefined;
            "scroll-snap-margin-right"?: import("csstype").Property.ScrollMarginRight<string | number> | undefined;
            "scroll-snap-margin-top"?: import("csstype").Property.ScrollMarginTop<string | number> | undefined;
            "scroll-snap-stop"?: import("csstype").Property.ScrollSnapStop | undefined;
            "scroll-snap-type"?: import("csstype").Property.ScrollSnapType | undefined;
            "scroll-timeline-axis"?: import("csstype").Property.ScrollTimelineAxis | undefined;
            "scroll-timeline-name"?: import("csstype").Property.ScrollTimelineName | undefined;
            "scrollbar-color"?: import("csstype").Property.ScrollbarColor | undefined;
            "scrollbar-gutter"?: import("csstype").Property.ScrollbarGutter | undefined;
            "scrollbar-width"?: import("csstype").Property.ScrollbarWidth | undefined;
            "shape-image-threshold"?: import("csstype").Property.ShapeImageThreshold | undefined;
            "shape-margin"?: import("csstype").Property.ShapeMargin<string | number> | undefined;
            "shape-outside"?: import("csstype").Property.ShapeOutside | undefined;
            "tab-size"?: import("csstype").Property.TabSize<string | number> | undefined;
            "table-layout"?: import("csstype").Property.TableLayout | undefined;
            "text-align"?: import("csstype").Property.TextAlign | undefined;
            "text-align-last"?: import("csstype").Property.TextAlignLast | undefined;
            "text-combine-upright"?: import("csstype").Property.TextCombineUpright | undefined;
            "text-decoration-color"?: import("csstype").Property.TextDecorationColor | undefined;
            "text-decoration-line"?: import("csstype").Property.TextDecorationLine | undefined;
            "text-decoration-skip"?: import("csstype").Property.TextDecorationSkip | undefined;
            "text-decoration-skip-ink"?: import("csstype").Property.TextDecorationSkipInk | undefined;
            "text-decoration-style"?: import("csstype").Property.TextDecorationStyle | undefined;
            "text-decoration-thickness"?: import("csstype").Property.TextDecorationThickness<string | number> | undefined;
            "text-emphasis-color"?: import("csstype").Property.TextEmphasisColor | undefined;
            "text-emphasis-position"?: import("csstype").Property.TextEmphasisPosition | undefined;
            "text-emphasis-style"?: import("csstype").Property.TextEmphasisStyle | undefined;
            "text-indent"?: import("csstype").Property.TextIndent<string | number> | undefined;
            "text-justify"?: import("csstype").Property.TextJustify | undefined;
            "text-orientation"?: import("csstype").Property.TextOrientation | undefined;
            "text-overflow"?: import("csstype").Property.TextOverflow | undefined;
            "text-rendering"?: import("csstype").Property.TextRendering | undefined;
            "text-shadow"?: import("csstype").Property.TextShadow | undefined;
            "text-size-adjust"?: import("csstype").Property.TextSizeAdjust | undefined;
            "text-transform"?: import("csstype").Property.TextTransform | undefined;
            "text-underline-offset"?: import("csstype").Property.TextUnderlineOffset<string | number> | undefined;
            "text-underline-position"?: import("csstype").Property.TextUnderlinePosition | undefined;
            "text-wrap"?: import("csstype").Property.TextWrap | undefined;
            "timeline-scope"?: import("csstype").Property.TimelineScope | undefined;
            "touch-action"?: import("csstype").Property.TouchAction | undefined;
            "transform-box"?: import("csstype").Property.TransformBox | undefined;
            "transform-origin"?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            "transform-style"?: import("csstype").Property.TransformStyle | undefined;
            "transition-behavior"?: import("csstype").Property.TransitionBehavior | undefined;
            "transition-delay"?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            "transition-duration"?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            "transition-property"?: import("csstype").Property.TransitionProperty | undefined;
            "transition-timing-function"?: import("csstype").Property.TransitionTimingFunction | undefined;
            "unicode-bidi"?: import("csstype").Property.UnicodeBidi | undefined;
            "user-select"?: import("csstype").Property.UserSelect | undefined;
            "vertical-align"?: import("csstype").Property.VerticalAlign<string | number> | undefined;
            "view-timeline-axis"?: import("csstype").Property.ViewTimelineAxis | undefined;
            "view-timeline-inset"?: import("csstype").Property.ViewTimelineInset<string | number> | undefined;
            "view-timeline-name"?: import("csstype").Property.ViewTimelineName | undefined;
            "view-transition-name"?: import("csstype").Property.ViewTransitionName | undefined;
            "white-space"?: import("csstype").Property.WhiteSpace | undefined;
            "white-space-collapse"?: import("csstype").Property.WhiteSpaceCollapse | undefined;
            "white-space-trim"?: import("csstype").Property.WhiteSpaceTrim | undefined;
            "will-change"?: import("csstype").Property.WillChange | undefined;
            "word-break"?: import("csstype").Property.WordBreak | undefined;
            "word-spacing"?: import("csstype").Property.WordSpacing<string | number> | undefined;
            "word-wrap"?: import("csstype").Property.WordWrap | undefined;
            "writing-mode"?: import("csstype").Property.WritingMode | undefined;
            "z-index"?: import("csstype").Property.ZIndex | undefined;
            "animation-range"?: import("csstype").Property.AnimationRange<string | number> | undefined;
            "background-position"?: import("csstype").Property.BackgroundPosition<string | number> | undefined;
            "border-block"?: import("csstype").Property.BorderBlock<string | number> | undefined;
            "border-block-end"?: import("csstype").Property.BorderBlockEnd<string | number> | undefined;
            "border-block-start"?: import("csstype").Property.BorderBlockStart<string | number> | undefined;
            "border-bottom"?: import("csstype").Property.BorderBottom<string | number> | undefined;
            "border-color"?: import("csstype").Property.BorderColor | undefined;
            "border-image"?: import("csstype").Property.BorderImage | undefined;
            "border-inline"?: import("csstype").Property.BorderInline<string | number> | undefined;
            "border-inline-end"?: import("csstype").Property.BorderInlineEnd<string | number> | undefined;
            "border-inline-start"?: import("csstype").Property.BorderInlineStart<string | number> | undefined;
            "border-left"?: import("csstype").Property.BorderLeft<string | number> | undefined;
            "border-radius"?: import("csstype").Property.BorderRadius<string | number> | undefined;
            "border-right"?: import("csstype").Property.BorderRight<string | number> | undefined;
            "border-style"?: import("csstype").Property.BorderStyle | undefined;
            "border-top"?: import("csstype").Property.BorderTop<string | number> | undefined;
            "border-width"?: import("csstype").Property.BorderWidth<string | number> | undefined;
            "column-rule"?: import("csstype").Property.ColumnRule<string | number> | undefined;
            "contain-intrinsic-size"?: import("csstype").Property.ContainIntrinsicSize<string | number> | undefined;
            "flex-flow"?: import("csstype").Property.FlexFlow | undefined;
            "grid-area"?: import("csstype").Property.GridArea | undefined;
            "grid-column"?: import("csstype").Property.GridColumn | undefined;
            "grid-row"?: import("csstype").Property.GridRow | undefined;
            "grid-template"?: import("csstype").Property.GridTemplate | undefined;
            "inset-block"?: import("csstype").Property.InsetBlock<string | number> | undefined;
            "inset-inline"?: import("csstype").Property.InsetInline<string | number> | undefined;
            "line-clamp"?: import("csstype").Property.LineClamp | undefined;
            "list-style"?: import("csstype").Property.ListStyle | undefined;
            "margin-block"?: import("csstype").Property.MarginBlock<string | number> | undefined;
            "margin-inline"?: import("csstype").Property.MarginInline<string | number> | undefined;
            "mask-border"?: import("csstype").Property.MaskBorder | undefined;
            "overscroll-behavior"?: import("csstype").Property.OverscrollBehavior | undefined;
            "padding-block"?: import("csstype").Property.PaddingBlock<string | number> | undefined;
            "padding-inline"?: import("csstype").Property.PaddingInline<string | number> | undefined;
            "place-content"?: import("csstype").Property.PlaceContent | undefined;
            "place-items"?: import("csstype").Property.PlaceItems | undefined;
            "place-self"?: import("csstype").Property.PlaceSelf | undefined;
            "scroll-margin"?: import("csstype").Property.ScrollMargin<string | number> | undefined;
            "scroll-margin-block"?: import("csstype").Property.ScrollMarginBlock<string | number> | undefined;
            "scroll-margin-inline"?: import("csstype").Property.ScrollMarginInline<string | number> | undefined;
            "scroll-padding"?: import("csstype").Property.ScrollPadding<string | number> | undefined;
            "scroll-padding-block"?: import("csstype").Property.ScrollPaddingBlock<string | number> | undefined;
            "scroll-padding-inline"?: import("csstype").Property.ScrollPaddingInline<string | number> | undefined;
            "scroll-snap-margin"?: import("csstype").Property.ScrollMargin<string | number> | undefined;
            "scroll-timeline"?: import("csstype").Property.ScrollTimeline | undefined;
            "text-decoration"?: import("csstype").Property.TextDecoration<string | number> | undefined;
            "text-emphasis"?: import("csstype").Property.TextEmphasis | undefined;
            "view-timeline"?: import("csstype").Property.ViewTimeline | undefined;
            "-moz-animation-delay"?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            "-moz-animation-direction"?: import("csstype").Property.AnimationDirection | undefined;
            "-moz-animation-duration"?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            "-moz-animation-fill-mode"?: import("csstype").Property.AnimationFillMode | undefined;
            "-moz-animation-iteration-count"?: import("csstype").Property.AnimationIterationCount | undefined;
            "-moz-animation-name"?: import("csstype").Property.AnimationName | undefined;
            "-moz-animation-play-state"?: import("csstype").Property.AnimationPlayState | undefined;
            "-moz-animation-timing-function"?: import("csstype").Property.AnimationTimingFunction | undefined;
            "-moz-appearance"?: import("csstype").Property.MozAppearance | undefined;
            "-moz-binding"?: import("csstype").Property.MozBinding | undefined;
            "-moz-border-bottom-colors"?: import("csstype").Property.MozBorderBottomColors | undefined;
            "-moz-border-end-color"?: import("csstype").Property.BorderInlineEndColor | undefined;
            "-moz-border-end-style"?: import("csstype").Property.BorderInlineEndStyle | undefined;
            "-moz-border-end-width"?: import("csstype").Property.BorderInlineEndWidth<string | number> | undefined;
            "-moz-border-left-colors"?: import("csstype").Property.MozBorderLeftColors | undefined;
            "-moz-border-right-colors"?: import("csstype").Property.MozBorderRightColors | undefined;
            "-moz-border-start-color"?: import("csstype").Property.BorderInlineStartColor | undefined;
            "-moz-border-start-style"?: import("csstype").Property.BorderInlineStartStyle | undefined;
            "-moz-border-top-colors"?: import("csstype").Property.MozBorderTopColors | undefined;
            "-moz-box-sizing"?: import("csstype").Property.BoxSizing | undefined;
            "-moz-column-count"?: import("csstype").Property.ColumnCount | undefined;
            "-moz-column-fill"?: import("csstype").Property.ColumnFill | undefined;
            "-moz-column-rule-color"?: import("csstype").Property.ColumnRuleColor | undefined;
            "-moz-column-rule-style"?: import("csstype").Property.ColumnRuleStyle | undefined;
            "-moz-column-rule-width"?: import("csstype").Property.ColumnRuleWidth<string | number> | undefined;
            "-moz-column-width"?: import("csstype").Property.ColumnWidth<string | number> | undefined;
            "-moz-context-properties"?: import("csstype").Property.MozContextProperties | undefined;
            "-moz-font-feature-settings"?: import("csstype").Property.FontFeatureSettings | undefined;
            "-moz-font-language-override"?: import("csstype").Property.FontLanguageOverride | undefined;
            "-moz-hyphens"?: import("csstype").Property.Hyphens | undefined;
            "-moz-image-region"?: import("csstype").Property.MozImageRegion | undefined;
            "-moz-margin-end"?: import("csstype").Property.MarginInlineEnd<string | number> | undefined;
            "-moz-margin-start"?: import("csstype").Property.MarginInlineStart<string | number> | undefined;
            "-moz-orient"?: import("csstype").Property.MozOrient | undefined;
            "-moz-osx-font-smoothing"?: import("csstype").Property.FontSmooth<string | number> | undefined;
            "-moz-outline-radius-bottomleft"?: import("csstype").Property.MozOutlineRadiusBottomleft<string | number> | undefined;
            "-moz-outline-radius-bottomright"?: import("csstype").Property.MozOutlineRadiusBottomright<string | number> | undefined;
            "-moz-outline-radius-topleft"?: import("csstype").Property.MozOutlineRadiusTopleft<string | number> | undefined;
            "-moz-outline-radius-topright"?: import("csstype").Property.MozOutlineRadiusTopright<string | number> | undefined;
            "-moz-padding-end"?: import("csstype").Property.PaddingInlineEnd<string | number> | undefined;
            "-moz-padding-start"?: import("csstype").Property.PaddingInlineStart<string | number> | undefined;
            "-moz-stack-sizing"?: import("csstype").Property.MozStackSizing | undefined;
            "-moz-tab-size"?: import("csstype").Property.TabSize<string | number> | undefined;
            "-moz-text-blink"?: import("csstype").Property.MozTextBlink | undefined;
            "-moz-text-size-adjust"?: import("csstype").Property.TextSizeAdjust | undefined;
            "-moz-user-focus"?: import("csstype").Property.MozUserFocus | undefined;
            "-moz-user-modify"?: import("csstype").Property.MozUserModify | undefined;
            "-moz-user-select"?: import("csstype").Property.UserSelect | undefined;
            "-moz-window-dragging"?: import("csstype").Property.MozWindowDragging | undefined;
            "-moz-window-shadow"?: import("csstype").Property.MozWindowShadow | undefined;
            "-ms-accelerator"?: import("csstype").Property.MsAccelerator | undefined;
            "-ms-block-progression"?: import("csstype").Property.MsBlockProgression | undefined;
            "-ms-content-zoom-chaining"?: import("csstype").Property.MsContentZoomChaining | undefined;
            "-ms-content-zoom-limit-max"?: import("csstype").Property.MsContentZoomLimitMax | undefined;
            "-ms-content-zoom-limit-min"?: import("csstype").Property.MsContentZoomLimitMin | undefined;
            "-ms-content-zoom-snap-points"?: import("csstype").Property.MsContentZoomSnapPoints | undefined;
            "-ms-content-zoom-snap-type"?: import("csstype").Property.MsContentZoomSnapType | undefined;
            "-ms-content-zooming"?: import("csstype").Property.MsContentZooming | undefined;
            "-ms-filter"?: import("csstype").Property.MsFilter | undefined;
            "-ms-flex-direction"?: import("csstype").Property.FlexDirection | undefined;
            "-ms-flex-positive"?: import("csstype").Property.FlexGrow | undefined;
            "-ms-flow-from"?: import("csstype").Property.MsFlowFrom | undefined;
            "-ms-flow-into"?: import("csstype").Property.MsFlowInto | undefined;
            "-ms-grid-columns"?: import("csstype").Property.MsGridColumns<string | number> | undefined;
            "-ms-grid-rows"?: import("csstype").Property.MsGridRows<string | number> | undefined;
            "-ms-high-contrast-adjust"?: import("csstype").Property.MsHighContrastAdjust | undefined;
            "-ms-hyphenate-limit-chars"?: import("csstype").Property.MsHyphenateLimitChars | undefined;
            "-ms-hyphenate-limit-lines"?: import("csstype").Property.MsHyphenateLimitLines | undefined;
            "-ms-hyphenate-limit-zone"?: import("csstype").Property.MsHyphenateLimitZone<string | number> | undefined;
            "-ms-hyphens"?: import("csstype").Property.Hyphens | undefined;
            "-ms-ime-align"?: import("csstype").Property.MsImeAlign | undefined;
            "-ms-line-break"?: import("csstype").Property.LineBreak | undefined;
            "-ms-order"?: import("csstype").Property.Order | undefined;
            "-ms-overflow-style"?: import("csstype").Property.MsOverflowStyle | undefined;
            "-ms-overflow-x"?: import("csstype").Property.OverflowX | undefined;
            "-ms-overflow-y"?: import("csstype").Property.OverflowY | undefined;
            "-ms-scroll-chaining"?: import("csstype").Property.MsScrollChaining | undefined;
            "-ms-scroll-limit-x-max"?: import("csstype").Property.MsScrollLimitXMax<string | number> | undefined;
            "-ms-scroll-limit-x-min"?: import("csstype").Property.MsScrollLimitXMin<string | number> | undefined;
            "-ms-scroll-limit-y-max"?: import("csstype").Property.MsScrollLimitYMax<string | number> | undefined;
            "-ms-scroll-limit-y-min"?: import("csstype").Property.MsScrollLimitYMin<string | number> | undefined;
            "-ms-scroll-rails"?: import("csstype").Property.MsScrollRails | undefined;
            "-ms-scroll-snap-points-x"?: import("csstype").Property.MsScrollSnapPointsX | undefined;
            "-ms-scroll-snap-points-y"?: import("csstype").Property.MsScrollSnapPointsY | undefined;
            "-ms-scroll-snap-type"?: import("csstype").Property.MsScrollSnapType | undefined;
            "-ms-scroll-translation"?: import("csstype").Property.MsScrollTranslation | undefined;
            "-ms-scrollbar-3dlight-color"?: import("csstype").Property.MsScrollbar3dlightColor | undefined;
            "-ms-scrollbar-arrow-color"?: import("csstype").Property.MsScrollbarArrowColor | undefined;
            "-ms-scrollbar-base-color"?: import("csstype").Property.MsScrollbarBaseColor | undefined;
            "-ms-scrollbar-darkshadow-color"?: import("csstype").Property.MsScrollbarDarkshadowColor | undefined;
            "-ms-scrollbar-face-color"?: import("csstype").Property.MsScrollbarFaceColor | undefined;
            "-ms-scrollbar-highlight-color"?: import("csstype").Property.MsScrollbarHighlightColor | undefined;
            "-ms-scrollbar-shadow-color"?: import("csstype").Property.MsScrollbarShadowColor | undefined;
            "-ms-scrollbar-track-color"?: import("csstype").Property.MsScrollbarTrackColor | undefined;
            "-ms-text-autospace"?: import("csstype").Property.MsTextAutospace | undefined;
            "-ms-text-combine-horizontal"?: import("csstype").Property.TextCombineUpright | undefined;
            "-ms-text-overflow"?: import("csstype").Property.TextOverflow | undefined;
            "-ms-touch-action"?: import("csstype").Property.TouchAction | undefined;
            "-ms-touch-select"?: import("csstype").Property.MsTouchSelect | undefined;
            "-ms-transform"?: import("csstype").Property.Transform | undefined;
            "-ms-transform-origin"?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            "-ms-transition-delay"?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            "-ms-transition-duration"?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            "-ms-transition-property"?: import("csstype").Property.TransitionProperty | undefined;
            "-ms-transition-timing-function"?: import("csstype").Property.TransitionTimingFunction | undefined;
            "-ms-user-select"?: import("csstype").Property.MsUserSelect | undefined;
            "-ms-word-break"?: import("csstype").Property.WordBreak | undefined;
            "-ms-wrap-flow"?: import("csstype").Property.MsWrapFlow | undefined;
            "-ms-wrap-margin"?: import("csstype").Property.MsWrapMargin<string | number> | undefined;
            "-ms-wrap-through"?: import("csstype").Property.MsWrapThrough | undefined;
            "-ms-writing-mode"?: import("csstype").Property.WritingMode | undefined;
            "-webkit-align-content"?: import("csstype").Property.AlignContent | undefined;
            "-webkit-align-items"?: import("csstype").Property.AlignItems | undefined;
            "-webkit-align-self"?: import("csstype").Property.AlignSelf | undefined;
            "-webkit-animation-delay"?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            "-webkit-animation-direction"?: import("csstype").Property.AnimationDirection | undefined;
            "-webkit-animation-duration"?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            "-webkit-animation-fill-mode"?: import("csstype").Property.AnimationFillMode | undefined;
            "-webkit-animation-iteration-count"?: import("csstype").Property.AnimationIterationCount | undefined;
            "-webkit-animation-name"?: import("csstype").Property.AnimationName | undefined;
            "-webkit-animation-play-state"?: import("csstype").Property.AnimationPlayState | undefined;
            "-webkit-animation-timing-function"?: import("csstype").Property.AnimationTimingFunction | undefined;
            "-webkit-appearance"?: import("csstype").Property.WebkitAppearance | undefined;
            "-webkit-backdrop-filter"?: import("csstype").Property.BackdropFilter | undefined;
            "-webkit-backface-visibility"?: import("csstype").Property.BackfaceVisibility | undefined;
            "-webkit-background-clip"?: import("csstype").Property.BackgroundClip | undefined;
            "-webkit-background-origin"?: import("csstype").Property.BackgroundOrigin | undefined;
            "-webkit-background-size"?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            "-webkit-border-before-color"?: import("csstype").Property.WebkitBorderBeforeColor | undefined;
            "-webkit-border-before-style"?: import("csstype").Property.WebkitBorderBeforeStyle | undefined;
            "-webkit-border-before-width"?: import("csstype").Property.WebkitBorderBeforeWidth<string | number> | undefined;
            "-webkit-border-bottom-left-radius"?: import("csstype").Property.BorderBottomLeftRadius<string | number> | undefined;
            "-webkit-border-bottom-right-radius"?: import("csstype").Property.BorderBottomRightRadius<string | number> | undefined;
            "-webkit-border-image-slice"?: import("csstype").Property.BorderImageSlice | undefined;
            "-webkit-border-top-left-radius"?: import("csstype").Property.BorderTopLeftRadius<string | number> | undefined;
            "-webkit-border-top-right-radius"?: import("csstype").Property.BorderTopRightRadius<string | number> | undefined;
            "-webkit-box-decoration-break"?: import("csstype").Property.BoxDecorationBreak | undefined;
            "-webkit-box-reflect"?: import("csstype").Property.WebkitBoxReflect<string | number> | undefined;
            "-webkit-box-shadow"?: import("csstype").Property.BoxShadow | undefined;
            "-webkit-box-sizing"?: import("csstype").Property.BoxSizing | undefined;
            "-webkit-clip-path"?: import("csstype").Property.ClipPath | undefined;
            "-webkit-column-count"?: import("csstype").Property.ColumnCount | undefined;
            "-webkit-column-fill"?: import("csstype").Property.ColumnFill | undefined;
            "-webkit-column-rule-color"?: import("csstype").Property.ColumnRuleColor | undefined;
            "-webkit-column-rule-style"?: import("csstype").Property.ColumnRuleStyle | undefined;
            "-webkit-column-rule-width"?: import("csstype").Property.ColumnRuleWidth<string | number> | undefined;
            "-webkit-column-span"?: import("csstype").Property.ColumnSpan | undefined;
            "-webkit-column-width"?: import("csstype").Property.ColumnWidth<string | number> | undefined;
            "-webkit-filter"?: import("csstype").Property.Filter | undefined;
            "-webkit-flex-basis"?: import("csstype").Property.FlexBasis<string | number> | undefined;
            "-webkit-flex-direction"?: import("csstype").Property.FlexDirection | undefined;
            "-webkit-flex-grow"?: import("csstype").Property.FlexGrow | undefined;
            "-webkit-flex-shrink"?: import("csstype").Property.FlexShrink | undefined;
            "-webkit-flex-wrap"?: import("csstype").Property.FlexWrap | undefined;
            "-webkit-font-feature-settings"?: import("csstype").Property.FontFeatureSettings | undefined;
            "-webkit-font-kerning"?: import("csstype").Property.FontKerning | undefined;
            "-webkit-font-smoothing"?: import("csstype").Property.FontSmooth<string | number> | undefined;
            "-webkit-font-variant-ligatures"?: import("csstype").Property.FontVariantLigatures | undefined;
            "-webkit-hyphenate-character"?: import("csstype").Property.HyphenateCharacter | undefined;
            "-webkit-hyphens"?: import("csstype").Property.Hyphens | undefined;
            "-webkit-initial-letter"?: import("csstype").Property.InitialLetter | undefined;
            "-webkit-justify-content"?: import("csstype").Property.JustifyContent | undefined;
            "-webkit-line-break"?: import("csstype").Property.LineBreak | undefined;
            "-webkit-line-clamp"?: import("csstype").Property.WebkitLineClamp | undefined;
            "-webkit-margin-end"?: import("csstype").Property.MarginInlineEnd<string | number> | undefined;
            "-webkit-margin-start"?: import("csstype").Property.MarginInlineStart<string | number> | undefined;
            "-webkit-mask-attachment"?: import("csstype").Property.WebkitMaskAttachment | undefined;
            "-webkit-mask-box-image-outset"?: import("csstype").Property.MaskBorderOutset<string | number> | undefined;
            "-webkit-mask-box-image-repeat"?: import("csstype").Property.MaskBorderRepeat | undefined;
            "-webkit-mask-box-image-slice"?: import("csstype").Property.MaskBorderSlice | undefined;
            "-webkit-mask-box-image-source"?: import("csstype").Property.MaskBorderSource | undefined;
            "-webkit-mask-box-image-width"?: import("csstype").Property.MaskBorderWidth<string | number> | undefined;
            "-webkit-mask-clip"?: import("csstype").Property.WebkitMaskClip | undefined;
            "-webkit-mask-composite"?: import("csstype").Property.WebkitMaskComposite | undefined;
            "-webkit-mask-image"?: import("csstype").Property.WebkitMaskImage | undefined;
            "-webkit-mask-origin"?: import("csstype").Property.WebkitMaskOrigin | undefined;
            "-webkit-mask-position"?: import("csstype").Property.WebkitMaskPosition<string | number> | undefined;
            "-webkit-mask-position-x"?: import("csstype").Property.WebkitMaskPositionX<string | number> | undefined;
            "-webkit-mask-position-y"?: import("csstype").Property.WebkitMaskPositionY<string | number> | undefined;
            "-webkit-mask-repeat"?: import("csstype").Property.WebkitMaskRepeat | undefined;
            "-webkit-mask-repeat-x"?: import("csstype").Property.WebkitMaskRepeatX | undefined;
            "-webkit-mask-repeat-y"?: import("csstype").Property.WebkitMaskRepeatY | undefined;
            "-webkit-mask-size"?: import("csstype").Property.WebkitMaskSize<string | number> | undefined;
            "-webkit-max-inline-size"?: import("csstype").Property.MaxInlineSize<string | number> | undefined;
            "-webkit-order"?: import("csstype").Property.Order | undefined;
            "-webkit-overflow-scrolling"?: import("csstype").Property.WebkitOverflowScrolling | undefined;
            "-webkit-padding-end"?: import("csstype").Property.PaddingInlineEnd<string | number> | undefined;
            "-webkit-padding-start"?: import("csstype").Property.PaddingInlineStart<string | number> | undefined;
            "-webkit-perspective"?: import("csstype").Property.Perspective<string | number> | undefined;
            "-webkit-perspective-origin"?: import("csstype").Property.PerspectiveOrigin<string | number> | undefined;
            "-webkit-print-color-adjust"?: import("csstype").Property.PrintColorAdjust | undefined;
            "-webkit-ruby-position"?: import("csstype").Property.RubyPosition | undefined;
            "-webkit-scroll-snap-type"?: import("csstype").Property.ScrollSnapType | undefined;
            "-webkit-shape-margin"?: import("csstype").Property.ShapeMargin<string | number> | undefined;
            "-webkit-tap-highlight-color"?: import("csstype").Property.WebkitTapHighlightColor | undefined;
            "-webkit-text-combine"?: import("csstype").Property.TextCombineUpright | undefined;
            "-webkit-text-decoration-color"?: import("csstype").Property.TextDecorationColor | undefined;
            "-webkit-text-decoration-line"?: import("csstype").Property.TextDecorationLine | undefined;
            "-webkit-text-decoration-skip"?: import("csstype").Property.TextDecorationSkip | undefined;
            "-webkit-text-decoration-style"?: import("csstype").Property.TextDecorationStyle | undefined;
            "-webkit-text-emphasis-color"?: import("csstype").Property.TextEmphasisColor | undefined;
            "-webkit-text-emphasis-position"?: import("csstype").Property.TextEmphasisPosition | undefined;
            "-webkit-text-emphasis-style"?: import("csstype").Property.TextEmphasisStyle | undefined;
            "-webkit-text-fill-color"?: import("csstype").Property.WebkitTextFillColor | undefined;
            "-webkit-text-orientation"?: import("csstype").Property.TextOrientation | undefined;
            "-webkit-text-size-adjust"?: import("csstype").Property.TextSizeAdjust | undefined;
            "-webkit-text-stroke-color"?: import("csstype").Property.WebkitTextStrokeColor | undefined;
            "-webkit-text-stroke-width"?: import("csstype").Property.WebkitTextStrokeWidth<string | number> | undefined;
            "-webkit-text-underline-position"?: import("csstype").Property.TextUnderlinePosition | undefined;
            "-webkit-touch-callout"?: import("csstype").Property.WebkitTouchCallout | undefined;
            "-webkit-transform"?: import("csstype").Property.Transform | undefined;
            "-webkit-transform-origin"?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            "-webkit-transform-style"?: import("csstype").Property.TransformStyle | undefined;
            "-webkit-transition-delay"?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            "-webkit-transition-duration"?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            "-webkit-transition-property"?: import("csstype").Property.TransitionProperty | undefined;
            "-webkit-transition-timing-function"?: import("csstype").Property.TransitionTimingFunction | undefined;
            "-webkit-user-modify"?: import("csstype").Property.WebkitUserModify | undefined;
            "-webkit-user-select"?: import("csstype").Property.UserSelect | undefined;
            "-webkit-writing-mode"?: import("csstype").Property.WritingMode | undefined;
            "-moz-animation"?: import("csstype").Property.Animation<string & {}> | undefined;
            "-moz-border-image"?: import("csstype").Property.BorderImage | undefined;
            "-moz-column-rule"?: import("csstype").Property.ColumnRule<string | number> | undefined;
            "-moz-columns"?: import("csstype").Property.Columns<string | number> | undefined;
            "-moz-outline-radius"?: import("csstype").Property.MozOutlineRadius<string | number> | undefined;
            "-ms-content-zoom-limit"?: import("csstype").Property.MsContentZoomLimit | undefined;
            "-ms-content-zoom-snap"?: import("csstype").Property.MsContentZoomSnap | undefined;
            "-ms-flex"?: import("csstype").Property.Flex<string | number> | undefined;
            "-ms-scroll-limit"?: import("csstype").Property.MsScrollLimit | undefined;
            "-ms-scroll-snap-x"?: import("csstype").Property.MsScrollSnapX | undefined;
            "-ms-scroll-snap-y"?: import("csstype").Property.MsScrollSnapY | undefined;
            "-ms-transition"?: import("csstype").Property.Transition<string & {}> | undefined;
            "-webkit-animation"?: import("csstype").Property.Animation<string & {}> | undefined;
            "-webkit-border-before"?: import("csstype").Property.WebkitBorderBefore<string | number> | undefined;
            "-webkit-border-image"?: import("csstype").Property.BorderImage | undefined;
            "-webkit-border-radius"?: import("csstype").Property.BorderRadius<string | number> | undefined;
            "-webkit-column-rule"?: import("csstype").Property.ColumnRule<string | number> | undefined;
            "-webkit-columns"?: import("csstype").Property.Columns<string | number> | undefined;
            "-webkit-flex"?: import("csstype").Property.Flex<string | number> | undefined;
            "-webkit-flex-flow"?: import("csstype").Property.FlexFlow | undefined;
            "-webkit-mask"?: import("csstype").Property.WebkitMask<string | number> | undefined;
            "-webkit-mask-box-image"?: import("csstype").Property.MaskBorder | undefined;
            "-webkit-text-emphasis"?: import("csstype").Property.TextEmphasis | undefined;
            "-webkit-text-stroke"?: import("csstype").Property.WebkitTextStroke<string | number> | undefined;
            "-webkit-transition"?: import("csstype").Property.Transition<string & {}> | undefined;
            "box-align"?: import("csstype").Property.BoxAlign | undefined;
            "box-direction"?: import("csstype").Property.BoxDirection | undefined;
            "box-flex"?: import("csstype").Property.BoxFlex | undefined;
            "box-flex-group"?: import("csstype").Property.BoxFlexGroup | undefined;
            "box-lines"?: import("csstype").Property.BoxLines | undefined;
            "box-ordinal-group"?: import("csstype").Property.BoxOrdinalGroup | undefined;
            "box-orient"?: import("csstype").Property.BoxOrient | undefined;
            "box-pack"?: import("csstype").Property.BoxPack | undefined;
            "grid-column-gap"?: import("csstype").Property.GridColumnGap<string | number> | undefined;
            "grid-gap"?: import("csstype").Property.GridGap<string | number> | undefined;
            "grid-row-gap"?: import("csstype").Property.GridRowGap<string | number> | undefined;
            "ime-mode"?: import("csstype").Property.ImeMode | undefined;
            "offset-block"?: import("csstype").Property.InsetBlock<string | number> | undefined;
            "offset-block-end"?: import("csstype").Property.InsetBlockEnd<string | number> | undefined;
            "offset-block-start"?: import("csstype").Property.InsetBlockStart<string | number> | undefined;
            "offset-inline"?: import("csstype").Property.InsetInline<string | number> | undefined;
            "offset-inline-end"?: import("csstype").Property.InsetInlineEnd<string | number> | undefined;
            "offset-inline-start"?: import("csstype").Property.InsetInlineStart<string | number> | undefined;
            "scroll-snap-coordinate"?: import("csstype").Property.ScrollSnapCoordinate<string | number> | undefined;
            "scroll-snap-destination"?: import("csstype").Property.ScrollSnapDestination<string | number> | undefined;
            "scroll-snap-points-x"?: import("csstype").Property.ScrollSnapPointsX | undefined;
            "scroll-snap-points-y"?: import("csstype").Property.ScrollSnapPointsY | undefined;
            "scroll-snap-type-x"?: import("csstype").Property.ScrollSnapTypeX | undefined;
            "scroll-snap-type-y"?: import("csstype").Property.ScrollSnapTypeY | undefined;
            "-khtml-box-align"?: import("csstype").Property.BoxAlign | undefined;
            "-khtml-box-direction"?: import("csstype").Property.BoxDirection | undefined;
            "-khtml-box-flex"?: import("csstype").Property.BoxFlex | undefined;
            "-khtml-box-flex-group"?: import("csstype").Property.BoxFlexGroup | undefined;
            "-khtml-box-lines"?: import("csstype").Property.BoxLines | undefined;
            "-khtml-box-ordinal-group"?: import("csstype").Property.BoxOrdinalGroup | undefined;
            "-khtml-box-orient"?: import("csstype").Property.BoxOrient | undefined;
            "-khtml-box-pack"?: import("csstype").Property.BoxPack | undefined;
            "-khtml-line-break"?: import("csstype").Property.LineBreak | undefined;
            "-khtml-opacity"?: import("csstype").Property.Opacity | undefined;
            "-khtml-user-select"?: import("csstype").Property.UserSelect | undefined;
            "-moz-backface-visibility"?: import("csstype").Property.BackfaceVisibility | undefined;
            "-moz-background-clip"?: import("csstype").Property.BackgroundClip | undefined;
            "-moz-background-inline-policy"?: import("csstype").Property.BoxDecorationBreak | undefined;
            "-moz-background-origin"?: import("csstype").Property.BackgroundOrigin | undefined;
            "-moz-background-size"?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            "-moz-border-radius"?: import("csstype").Property.BorderRadius<string | number> | undefined;
            "-moz-border-radius-bottomleft"?: import("csstype").Property.BorderBottomLeftRadius<string | number> | undefined;
            "-moz-border-radius-bottomright"?: import("csstype").Property.BorderBottomRightRadius<string | number> | undefined;
            "-moz-border-radius-topleft"?: import("csstype").Property.BorderTopLeftRadius<string | number> | undefined;
            "-moz-border-radius-topright"?: import("csstype").Property.BorderTopRightRadius<string | number> | undefined;
            "-moz-box-align"?: import("csstype").Property.BoxAlign | undefined;
            "-moz-box-direction"?: import("csstype").Property.BoxDirection | undefined;
            "-moz-box-flex"?: import("csstype").Property.BoxFlex | undefined;
            "-moz-box-ordinal-group"?: import("csstype").Property.BoxOrdinalGroup | undefined;
            "-moz-box-orient"?: import("csstype").Property.BoxOrient | undefined;
            "-moz-box-pack"?: import("csstype").Property.BoxPack | undefined;
            "-moz-box-shadow"?: import("csstype").Property.BoxShadow | undefined;
            "-moz-float-edge"?: import("csstype").Property.MozFloatEdge | undefined;
            "-moz-force-broken-image-icon"?: import("csstype").Property.MozForceBrokenImageIcon | undefined;
            "-moz-opacity"?: import("csstype").Property.Opacity | undefined;
            "-moz-outline"?: import("csstype").Property.Outline<string | number> | undefined;
            "-moz-outline-color"?: import("csstype").Property.OutlineColor | undefined;
            "-moz-outline-style"?: import("csstype").Property.OutlineStyle | undefined;
            "-moz-outline-width"?: import("csstype").Property.OutlineWidth<string | number> | undefined;
            "-moz-perspective"?: import("csstype").Property.Perspective<string | number> | undefined;
            "-moz-perspective-origin"?: import("csstype").Property.PerspectiveOrigin<string | number> | undefined;
            "-moz-text-align-last"?: import("csstype").Property.TextAlignLast | undefined;
            "-moz-text-decoration-color"?: import("csstype").Property.TextDecorationColor | undefined;
            "-moz-text-decoration-line"?: import("csstype").Property.TextDecorationLine | undefined;
            "-moz-text-decoration-style"?: import("csstype").Property.TextDecorationStyle | undefined;
            "-moz-transform"?: import("csstype").Property.Transform | undefined;
            "-moz-transform-origin"?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            "-moz-transform-style"?: import("csstype").Property.TransformStyle | undefined;
            "-moz-transition"?: import("csstype").Property.Transition<string & {}> | undefined;
            "-moz-transition-delay"?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            "-moz-transition-duration"?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            "-moz-transition-property"?: import("csstype").Property.TransitionProperty | undefined;
            "-moz-transition-timing-function"?: import("csstype").Property.TransitionTimingFunction | undefined;
            "-moz-user-input"?: import("csstype").Property.MozUserInput | undefined;
            "-ms-ime-mode"?: import("csstype").Property.ImeMode | undefined;
            "-o-animation"?: import("csstype").Property.Animation<string & {}> | undefined;
            "-o-animation-delay"?: import("csstype").Property.AnimationDelay<string & {}> | undefined;
            "-o-animation-direction"?: import("csstype").Property.AnimationDirection | undefined;
            "-o-animation-duration"?: import("csstype").Property.AnimationDuration<string & {}> | undefined;
            "-o-animation-fill-mode"?: import("csstype").Property.AnimationFillMode | undefined;
            "-o-animation-iteration-count"?: import("csstype").Property.AnimationIterationCount | undefined;
            "-o-animation-name"?: import("csstype").Property.AnimationName | undefined;
            "-o-animation-play-state"?: import("csstype").Property.AnimationPlayState | undefined;
            "-o-animation-timing-function"?: import("csstype").Property.AnimationTimingFunction | undefined;
            "-o-background-size"?: import("csstype").Property.BackgroundSize<string | number> | undefined;
            "-o-border-image"?: import("csstype").Property.BorderImage | undefined;
            "-o-object-fit"?: import("csstype").Property.ObjectFit | undefined;
            "-o-object-position"?: import("csstype").Property.ObjectPosition<string | number> | undefined;
            "-o-tab-size"?: import("csstype").Property.TabSize<string | number> | undefined;
            "-o-text-overflow"?: import("csstype").Property.TextOverflow | undefined;
            "-o-transform"?: import("csstype").Property.Transform | undefined;
            "-o-transform-origin"?: import("csstype").Property.TransformOrigin<string | number> | undefined;
            "-o-transition"?: import("csstype").Property.Transition<string & {}> | undefined;
            "-o-transition-delay"?: import("csstype").Property.TransitionDelay<string & {}> | undefined;
            "-o-transition-duration"?: import("csstype").Property.TransitionDuration<string & {}> | undefined;
            "-o-transition-property"?: import("csstype").Property.TransitionProperty | undefined;
            "-o-transition-timing-function"?: import("csstype").Property.TransitionTimingFunction | undefined;
            "-webkit-box-align"?: import("csstype").Property.BoxAlign | undefined;
            "-webkit-box-direction"?: import("csstype").Property.BoxDirection | undefined;
            "-webkit-box-flex"?: import("csstype").Property.BoxFlex | undefined;
            "-webkit-box-flex-group"?: import("csstype").Property.BoxFlexGroup | undefined;
            "-webkit-box-lines"?: import("csstype").Property.BoxLines | undefined;
            "-webkit-box-ordinal-group"?: import("csstype").Property.BoxOrdinalGroup | undefined;
            "-webkit-box-orient"?: import("csstype").Property.BoxOrient | undefined;
            "-webkit-box-pack"?: import("csstype").Property.BoxPack | undefined;
            "alignment-baseline"?: import("csstype").Property.AlignmentBaseline | undefined;
            "baseline-shift"?: import("csstype").Property.BaselineShift<string | number> | undefined;
            "clip-rule"?: import("csstype").Property.ClipRule | undefined;
            "color-interpolation"?: import("csstype").Property.ColorInterpolation | undefined;
            "color-rendering"?: import("csstype").Property.ColorRendering | undefined;
            "dominant-baseline"?: import("csstype").Property.DominantBaseline | undefined;
            "fill-opacity"?: import("csstype").Property.FillOpacity | undefined;
            "fill-rule"?: import("csstype").Property.FillRule | undefined;
            "flood-color"?: import("csstype").Property.FloodColor | undefined;
            "flood-opacity"?: import("csstype").Property.FloodOpacity | undefined;
            "glyph-orientation-vertical"?: import("csstype").Property.GlyphOrientationVertical | undefined;
            "lighting-color"?: import("csstype").Property.LightingColor | undefined;
            "marker-end"?: import("csstype").Property.MarkerEnd | undefined;
            "marker-mid"?: import("csstype").Property.MarkerMid | undefined;
            "marker-start"?: import("csstype").Property.MarkerStart | undefined;
            "shape-rendering"?: import("csstype").Property.ShapeRendering | undefined;
            "stop-color"?: import("csstype").Property.StopColor | undefined;
            "stop-opacity"?: import("csstype").Property.StopOpacity | undefined;
            "stroke-dasharray"?: import("csstype").Property.StrokeDasharray<string | number> | undefined;
            "stroke-dashoffset"?: import("csstype").Property.StrokeDashoffset<string | number> | undefined;
            "stroke-linecap"?: import("csstype").Property.StrokeLinecap | undefined;
            "stroke-linejoin"?: import("csstype").Property.StrokeLinejoin | undefined;
            "stroke-miterlimit"?: import("csstype").Property.StrokeMiterlimit | undefined;
            "stroke-opacity"?: import("csstype").Property.StrokeOpacity | undefined;
            "stroke-width"?: import("csstype").Property.StrokeWidth<string | number> | undefined;
            "text-anchor"?: import("csstype").Property.TextAnchor | undefined;
            "vector-effect"?: import("csstype").Property.VectorEffect | undefined;
        } | undefined;
        hoverStyle?: CSSPropertiess;
        imgVariant?: "rectangle" | "square" | undefined;
        imgPreview?: boolean | undefined;
        imgPreviewMask?: boolean | undefined;
        status?: "uploading" | "done" | "error" | undefined;
        percent?: numbe;
        errorTip?: string | undefined;
        id?: number | undefined;
    }[]>;
    handleBeforeUpload: (file: File) => boolean;
    handleUploadDrop: (dropped: File[], props: FileListProps) => false | undefined;
    handleHttpRequest: (options: {
        file: File;
    }) => Promise<void>;
    handleDeleteCard: (item: SelfFilesCardProps) => void;
};
