import { TagInfo, UserInfo } from 'chatarea';
declare const _default: import('vue').DefineComponent<{}, {}, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {}, string, import('vue').PublicProps, Readonly<{}> & Readonly<{}>, {}, {}, {}, {}, string, import('vue').ComponentProvideOptions, true, {
    editorRef: ({
        $: import('vue').ComponentInternalInstance;
        $data: {};
        $props: {
            readonly placeholder?: string | undefined;
            readonly device?: "pc" | "h5" | undefined;
            readonly autoFocus?: boolean | undefined;
            readonly variant?: "default" | "updown" | undefined;
            readonly userList?: import('../../components/EditorSender/types').UserInfo[] | undefined;
            readonly customTrigger?: import('../../components/EditorSender/types').CustomTag[] | undefined;
            readonly selectList?: import('../../components/EditorSender/types').SelectTag[] | undefined;
            readonly maxLength?: number | undefined;
            readonly submitType?: "enter" | "shiftEnter" | undefined;
            readonly customStyle?: Record<string, any> | undefined;
            readonly loading?: boolean | undefined;
            readonly disabled?: boolean | undefined;
            readonly clearable?: boolean | undefined;
            readonly headerAnimationTimer?: number | undefined;
            readonly asyncMatchFun?: ((searchVal: string) => Promise<import('../../components/EditorSender/types').UserInfo[]>) | undefined;
            readonly customDialog?: boolean | undefined;
            readonly onCancel?: (() => any) | undefined;
            readonly onChange?: (() => any) | undefined;
            readonly onSubmit?: ((payload: import('../../components/EditorSender/types').SubmitResult) => any) | undefined;
            readonly onShowAtDialog?: (() => any) | undefined;
            readonly onShowTagDialog?: ((prefix: string) => any) | undefined;
            readonly onShowSelectDialog?: ((key: string, elm: HTMLElement) => any) | undefined;
        } & import('vue').VNodeProps & import('vue').AllowedComponentProps & import('vue').ComponentCustomProps;
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        } & {
            container: HTMLDivElement;
        };
        $slots: Readonly<{
            [name: string]: globalThis.Slot | undefined;
        }>;
        $root: ComponentPublicInstance | null;
        $parent: ComponentPublicInstance | null;
        $host: Element | null;
        $emit: ((event: "cancel") => void) & ((event: "change") => void) & ((event: "submit", payload: import('../../components/EditorSender/types').SubmitResult) => void) & ((event: "showAtDialog") => void) & ((event: "showTagDialog", prefix: string) => void) & ((event: "showSelectDialog", key: string, elm: HTMLElement) => void);
        $el: HTMLDivElement;
        $options: import('vue').ComponentOptionsBase<Readonly<import('../../components/EditorSender/types').EditorProps> & Readonly<{
            onCancel?: (() => any) | undefined;
            onChange?: (() => any) | undefined;
            onSubmit?: ((payload: import('../../components/EditorSender/types').SubmitResult) => any) | undefined;
            onShowAtDialog?: (() => any) | undefined;
            onShowTagDialog?: ((prefix: string) => any) | undefined;
            onShowSelectDialog?: ((key: string, elm: HTMLElement) => any) | undefined;
        }>, {
            getCurrentValue: () => import('../../components/EditorSender/types').SubmitResult;
            focusToStart: () => void;
            focusToEnd: () => void;
            blur: () => void;
            selectAll: () => void;
            clear: (txt?: string) => void;
            setSelectTag: (key: string, tagId: string) => void;
            setInputTag: (key: string, placeholder: string, defaultValue?: string) => void;
            setUserTag: (userId: string) => void;
            setCustomTag: (prefix: string, id: string) => void;
            setMixTags: (tags: import('../../components/EditorSender/types').MixTag[][]) => void;
            setHtml: (html: string) => void;
            setText: (txt: string) => void;
            openSelectDialog: (option: import('../../components/EditorSender/types').SelectDialogOption) => void;
            customSetUser: (user: UserInfo) => void;
            customSetTag: (prefix: string, tag: TagInfo) => void;
            updateSelectTag: (elm: HTMLElement, tag: TagInfo) => void;
            openTipTag: (options: import('chatarea').TipOptions) => void;
            closeTipTag: () => void;
            chat: globalThis.Ref<import('chatarea').default<UserInfo> | undefined, import('chatarea').default<UserInfo> | undefined>;
            opNode: globalThis.Ref<import('chatarea').ChatOperateNode | undefined, import('chatarea').ChatOperateNode | undefined>;
            chatState: {
                isEmpty: boolean;
                textLength: number;
                lastFocusNode: Node | null;
                lastOffset: number;
                wrapCallSelectDialog: boolean;
                beforeText?: string | undefined;
                afterText?: string | undefined;
            };
        }, {}, {}, {}, import('vue').ComponentOptionsMixin, import('vue').ComponentOptionsMixin, {} & {
            cancel: () => any;
            change: () => any;
            submit: (payload: import('../../components/EditorSender/types').SubmitResult) => any;
            showAtDialog: () => any;
            showTagDialog: (prefix: string) => any;
            showSelectDialog: (key: string, elm: HTMLElement) => any;
        }, string, {
            disabled: boolean;
            placeholder: string;
            loading: boolean;
            variant: "default" | "updown";
            device: "pc" | "h5";
            autoFocus: boolean;
            userList: import('../../components/EditorSender/types').UserInfo[];
            customTrigger: import('../../components/EditorSender/types').CustomTag[];
            selectList: import('../../components/EditorSender/types').SelectTag[];
            maxLength: number;
            submitType: "enter" | "shiftEnter";
            customStyle: Record<string, any>;
            clearable: boolean;
            headerAnimationTimer: number;
            asyncMatchFun: (searchVal: string) => Promise<import('../../components/EditorSender/types').UserInfo[]>;
            customDialog: boolean;
        }, {}, string, {}, import('vue').GlobalComponents, import('vue').GlobalDirectives, string, import('vue').ComponentProvideOptions> & {
            beforeCreate?: (() => void) | (() => void)[];
            created?: (() => void) | (() => void)[];
            beforeMount?: (() => void) | (() => void)[];
            mounted?: (() => void) | (() => void)[];
            beforeUpdate?: (() => void) | (() => void)[];
            updated?: (() => void) | (() => void)[];
            activated?: (() => void) | (() => void)[];
            deactivated?: (() => void) | (() => void)[];
            beforeDestroy?: (() => void) | (() => void)[];
            beforeUnmount?: (() => void) | (() => void)[];
            destroyed?: (() => void) | (() => void)[];
            unmounted?: (() => void) | (() => void)[];
            renderTracked?: ((e: import('vue').DebuggerEvent) => void) | ((e: import('vue').DebuggerEvent) => void)[];
            renderTriggered?: ((e: import('vue').DebuggerEvent) => void) | ((e: import('vue').DebuggerEvent) => void)[];
            errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
        };
        $forceUpdate: () => void;
        $nextTick: typeof import('vue').nextTick;
        $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, import('@vue/reactivity').OnCleanup]) => any : (...args: [any, any, import('@vue/reactivity').OnCleanup]) => any, options?: import('vue').WatchOptions): import('vue').WatchStopHandle;
    } & Readonly<{
        disabled: boolean;
        placeholder: string;
        loading: boolean;
        variant: "default" | "updown";
        device: "pc" | "h5";
        autoFocus: boolean;
        userList: import('../../components/EditorSender/types').UserInfo[];
        customTrigger: import('../../components/EditorSender/types').CustomTag[];
        selectList: import('../../components/EditorSender/types').SelectTag[];
        maxLength: number;
        submitType: "enter" | "shiftEnter";
        customStyle: Record<string, any>;
        clearable: boolean;
        headerAnimationTimer: number;
        asyncMatchFun: (searchVal: string) => Promise<import('../../components/EditorSender/types').UserInfo[]>;
        customDialog: boolean;
    }> & Omit<Readonly<import('../../components/EditorSender/types').EditorProps> & Readonly<{
        onCancel?: (() => any) | undefined;
        onChange?: (() => any) | undefined;
        onSubmit?: ((payload: import('../../components/EditorSender/types').SubmitResult) => any) | undefined;
        onShowAtDialog?: (() => any) | undefined;
        onShowTagDialog?: ((prefix: string) => any) | undefined;
        onShowSelectDialog?: ((key: string, elm: HTMLElement) => any) | undefined;
    }>, "blur" | "clear" | "getCurrentValue" | "focusToStart" | "focusToEnd" | "selectAll" | "setSelectTag" | "setInputTag" | "setUserTag" | "setCustomTag" | "setMixTags" | "setHtml" | "setText" | "openSelectDialog" | "customSetUser" | "customSetTag" | "updateSelectTag" | "openTipTag" | "closeTipTag" | "chat" | "opNode" | "chatState" | ("disabled" | "placeholder" | "loading" | "variant" | "device" | "autoFocus" | "userList" | "customTrigger" | "selectList" | "maxLength" | "submitType" | "customStyle" | "clearable" | "headerAnimationTimer" | "asyncMatchFun" | "customDialog")> & import('vue').ShallowUnwrapRef<{
        getCurrentValue: () => import('../../components/EditorSender/types').SubmitResult;
        focusToStart: () => void;
        focusToEnd: () => void;
        blur: () => void;
        selectAll: () => void;
        clear: (txt?: string) => void;
        setSelectTag: (key: string, tagId: string) => void;
        setInputTag: (key: string, placeholder: string, defaultValue?: string) => void;
        setUserTag: (userId: string) => void;
        setCustomTag: (prefix: string, id: string) => void;
        setMixTags: (tags: import('../../components/EditorSender/types').MixTag[][]) => void;
        setHtml: (html: string) => void;
        setText: (txt: string) => void;
        openSelectDialog: (option: import('../../components/EditorSender/types').SelectDialogOption) => void;
        customSetUser: (user: UserInfo) => void;
        customSetTag: (prefix: string, tag: TagInfo) => void;
        updateSelectTag: (elm: HTMLElement, tag: TagInfo) => void;
        openTipTag: (options: import('chatarea').TipOptions) => void;
        closeTipTag: () => void;
        chat: globalThis.Ref<import('chatarea').default<UserInfo> | undefined, import('chatarea').default<UserInfo> | undefined>;
        opNode: globalThis.Ref<import('chatarea').ChatOperateNode | undefined, import('chatarea').ChatOperateNode | undefined>;
        chatState: {
            isEmpty: boolean;
            textLength: number;
            lastFocusNode: Node | null;
            lastOffset: number;
            wrapCallSelectDialog: boolean;
            beforeText?: string | undefined;
            afterText?: string | undefined;
        };
    }> & {} & import('vue').ComponentCustomProperties & {} & {
        $slots: {
            header?(_: {}): any;
            prefix?(_: {}): any;
            prefix?(_: {}): any;
            'action-list'?(_: {}): any;
            'action-list'?(_: {}): any;
            footer?(_: {}): any;
        };
    }) | null;
}, HTMLDivElement>;
export default _default;
